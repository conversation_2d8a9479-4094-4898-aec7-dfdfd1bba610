import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, Inject, Input, PLATFORM_ID, signal } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic, ComicStatus } from '@schema';
import { UrlService } from '@services/url.service';
import { OptimizedBaseComponent } from '../../base/optimized-base.component';

@Component({
    selector: 'div[app-comic-card-v2]',
    templateUrl: './comic-card-v2.component.html',
    styleUrl: './comic-card-v2.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, NumeralPipe, DateAgoPipe, ComicDescriptionPipe],
})
export class ComicCardV2Component extends OptimizedBaseComponent {
  @Input()
  set comic(value: Comic | undefined) {
    if (value !== this.comicSignal()) {
      this.comicSignal.set(value);
    }
  }
  get comic(): Comic | undefined {
    return this.comicSignal();
  }

  // Signals for reactive state
  private readonly comicSignal = signal<Comic | undefined>(undefined);

  // Computed properties for optimized access
  readonly comicData = computed(() => this.comicSignal());
  readonly comicRouterLink = computed(() => {
    const comic = this.comicData();
    return comic ? this.urlService.getComicDetailUrl(comic) : [];
  });
  readonly displayGenres = computed(() => this.comicData()?.genres?.slice(0, 3) || []);
  readonly isOngoing = computed(() => this.comicData()?.status === ComicStatus.ONGOING);
  readonly isCompleted = computed(() => this.comicData()?.status === ComicStatus.COMPLETED);
  readonly statusText = computed(() => this.isOngoing() ? 'Đang tiến hành' : 'Hoàn thành');
  readonly filledDescription = computed(() => {
    const comic = this.comicData();
    return comic ? (comic.description || '') : '';
  });

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private readonly urlService: UrlService
  ) {
    super(cdr, platformId);
  }

  trackByGenreId = (index: number, genre: any): number => {
    return genre?.id ?? index;
  };
}
