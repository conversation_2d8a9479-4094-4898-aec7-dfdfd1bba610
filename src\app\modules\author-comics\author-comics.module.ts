import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { AuthorComicsComponent } from './author-comics.component';

@NgModule({
  declarations: [AuthorComicsComponent],
  imports: [
    RouterModule.forChild([{ path: '', component: AuthorComicsComponent }]),
    CommonModule,
    BreadcrumbComponent,
    GridComicComponent,
    SpinnerComponent,
    EmptyComponent,
  ]
})
export class AuthorComicsModule { }
