import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, EventEmitter, Inject, Input, OnDestroy, Output, PLATFORM_ID, signal, TemplateRef } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { UrlService } from '@services/url.service';
import { OptimizedBaseComponent } from '../../base/optimized-base.component';

@Component({
    selector: 'div[app-comic-card]',
    templateUrl: './comic-card.component.html',
    styleUrl: './comic-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, NumeralPipe, DateAgoPipe],
})
export class ComicCardComponent extends OptimizedBaseComponent implements OnD<PERSON>roy {
  @Input()
  set comic(value: Comic | undefined) {
    if (value !== this.comicSignal()) {
      this.comicSignal.set(value);
    }
  }
  get comic(): Comic | undefined {
    return this.comicSignal();
  }

  @Output() comicHover = new EventEmitter<Comic | undefined>();
  @Input() topRightTemplate?: TemplateRef<any>;
  // Signals for reactive state
  private readonly comicSignal = signal<Comic | undefined>(undefined);

  // Computed properties for optimized access
  readonly comicData = computed(() => this.comicSignal());
  readonly comicRouterLink = computed(() => {
    const comic = this.comicData();
    return comic ? this.urlService.getComicDetailUrl(comic) : [];
  });
  readonly chapterRouterLink = computed(() => {
    const comic = this.comicData()
    if (!comic?.chapters?.[0]) return [];
    return this.urlService.getChapterDetailUrl(comic, comic.chapters[0]);
  });
  readonly hasChapters = computed(() => !!(this.comicData()?.chapters?.length));
  readonly authorName = computed(() => this.comicData()?.author ?? 'Đang cập nhật');

  constructor(
    cdr: ChangeDetectorRef,
    private urlService: UrlService,
    @Inject(PLATFORM_ID)  platformId: object
  ) {
    super(cdr, platformId);
  }

  onHoverComic = (hover: boolean): void => {
    this.comicHover.emit(hover && this.comic ? this.comic : undefined);
  };


  override ngOnDestroy(): void {
    this.comicHover.emit(undefined);
    super.ngOnDestroy();
  }
}
