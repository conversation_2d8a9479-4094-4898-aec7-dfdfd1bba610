import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter } from '@angular/core';
import { IPopupComponent } from 'src/app/core/interface';


@Component({
  imports: [CommonModule],
  selector: '[app-confirm-popup]',
  templateUrl: './confirm-popup.component.html',
  styleUrl: './confirm-popup.component.scss'
})
export class ConfirmPopupComponent implements IPopupComponent {
  isVisible = false;
  title = '';
  message = '';
  confirmButtonText = '';
  cancelButtonText = '';
  constructor(private cd: ChangeDetectorRef) { }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }

  confirm = new EventEmitter<void>();
  cancel = new EventEmitter<void>();
  handleConfirm() {
    this.confirm.emit();
    this.setVisible(false);
  }

  handleCancel() {
    this.cancel.emit();
    this.setVisible(false);

  }

  show(o: any) {
    const { title, message, confirmButtonText, cancelButtonText } = o;
    this.title = title;
    this.message = message;
    this.confirmButtonText = confirmButtonText;
    this.cancelButtonText = cancelButtonText;
    this.setVisible(true);
    return new Promise((resolve) => {
      //wait until handleConfirm
      const confirmSubscription = this.confirm.subscribe(() => {
        confirmSubscription.unsubscribe();
        resolve({ isconfirm: true, isCancel: false });
      });
      const cancelSubscription = this.cancel.subscribe(() => {
        cancelSubscription.unsubscribe();
        resolve({ isconfirm: false, isCancel: true });
      })
    });

  }
}
