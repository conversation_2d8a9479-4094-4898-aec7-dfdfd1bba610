// tutorial.service.ts

import { isPlatformBrowser } from "@angular/common";
import { Inject, Injectable, Optional, PLATFORM_ID, REQUEST } from "@angular/core";
import { Chapter, Comic } from "@schema";
import globalConfig from "globalConfig";

enum DomainType {
    Main = 0,
    Sub = 1,
}
@Injectable({ providedIn: 'root' })
export class UrlService {

    baseUrl = this.getBaseUrl();
    apiUrl = this.getApiUrl();
    host = this.getHost();
    get domainType() {
        return this.getDomainType(this.host);
    }
    constructor(@Inject(PLATFORM_ID) private platformId: object,
        @Optional() @Inject(REQUEST) private request: Request) {
    }
    public getDomainType(domain: string): DomainType {
        switch (domain) {
            case 'metruyenmoi.org':
                return DomainType.Sub;
            default:
                return DomainType.Main;
        }
    }
    buildUrl(path: string) {
        return this.apiUrl + path;
    }
    getHost(): string {
        if (isPlatformBrowser(this.platformId)) {
            return window.location.host;
        }
        const _host = this.request.headers.get("host");
        if (!_host) return globalConfig.BASE_URL;
        if (_host.includes(':')) return globalConfig.BASE_URL
        return _host;
    }
    getApiUrl(): string {
        if (isPlatformBrowser(this.platformId) && !window.location.origin.includes('localhost')) {
            return window.location.origin + '/api';
        }
        return globalConfig.BASE_API_URL + '/api';
    }

    getBaseUrl() {
        if (isPlatformBrowser(this.platformId)) {
            return window.location.origin;
        }
        const _host = this.request.headers.get("host");
        if (!_host) return globalConfig.BASE_URL;
        if (_host.includes(':')) return globalConfig.BASE_URL
        return `https://${_host}`;

    }

    getComicDetailUrl(comic: any): string[] {
        if (this.domainType === 1) {
            return ['/truyen-tranh', comic.url];
        }
        return ['/truyen-tranh', comic.url + '-' + comic.id];
    }
    // getChapterDetailUrl(comic: any, chapter: any): string {
    //     if (this.DomainType === 1) {
    //         return `/truyen-tranh/${comic.url}/chuong-${chapter.slug}`;
    //     }
    //     return `/truyen-tranh/${comic.url}/${chapter.id}`;
    // }
    getChapterDetailUrl(comic: Comic, chapter: Chapter): string[] {

        if (this.domainType === 1) {
            return ['/truyen-tranh', comic.url, 'chuong-' + chapter.slug];
        }
        return ['/truyen-tranh', comic.url, chapter.id.toString()];
    }
    getFullChapterUrl(comic: Comic, chapter: Chapter): string {
        if (this.domainType === 1) {
            return `${this.baseUrl}/truyen-tranh/${comic.url}/chuong-${chapter.slug}`;
        }
        return `${this.baseUrl}/truyen-tranh/${comic.url}/${chapter.id}`;
    }
    getFullComicUrl(comic: Comic): string {
        if (this.domainType === 1) {
            return `${this.baseUrl}/truyen-tranh/${comic.url}`;
        }
        return `${this.baseUrl}/truyen-tranh/${comic.url}-${comic.id}`;
    }
    getChapterDetailUrl2(comicurl:string, chapterurl:string): string[] {
        if (this.domainType === 1) {
            return ['/truyen-tranh', comicurl, 'chuong-' + chapterurl];
        }
        return ['/truyen-tranh', comicurl, chapterurl];
    }
}

interface DomainSettings {
    [domain: string]: {
        urlType?: number;
    }
}