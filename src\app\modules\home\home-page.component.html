<div id="content" class="home-content">
  <div app-anouncement></div>
  <div class="flex h-[36px] mt-3 flex-row justify-between gap-6 bg-dark-bg dark:bg-black rounded-t">
    <div class="min-w-32 flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
        class="text-white h-6 w-6 ml-2"
        viewBox="0 0 32 32"
        id="icon"
      >
        <defs>
          <style>
            .cls-1 {
              fill: none;
            }
          </style>
        </defs>
        <title>Truy<PERSON>n đang thịnh hành</title>
        <path
          d="M16,2a9,9,0,0,0-6,15.69V30l6-4,6,4V17.69A9,9,0,0,0,16,2Zm4,24.26-2.89-1.92L16,23.6l-1.11.74L12,26.26V19.05a8.88,8.88,0,0,0,8,0ZM20.89,16A7,7,0,1,1,23,11,7,7,0,0,1,20.89,16Z"
        />
        <rect
          id="_Transparent_Rectangle_"
          data-name="&lt;Transparent Rectangle&gt;"
          class="cls-1"
          width="32"
          height="32"
        />
      </svg>
      <h2 class="block-title">Truyện đang thịnh hành</h2>
    </div>
  </div>

  <div app-carousel-landing class="carousel-landing"></div>
  <div class="mt-4 grid grid-cols-1 xl:grid-cols-4 gap-2 lg:gap-4">
    <div id="comics" class="xl:col-span-3 row-span-3">
      <div
        app-grid-comic
        [listComics]="listComics"
        [nPreview]="ssr() ? 12 : 30"
        [title]="'Mới cập nhật'"
      >
        <ng-template #iconTemplate>
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path d="M19 18a3.5 3.5 0 0 0 0 -7h-1a5 4.5 0 0 0 -11 -2a4.6 4.4 0 0 0 -2.1 8.4" />
            <line x1="12" y1="13" x2="12" y2="22" />
            <polyline points="9 19 12 22 15 19" />
          </svg>
        </ng-template>
      </div>
      <div
        app-pagination
        *ngIf="!ssr()"
        (OnChange)="onChangePage($event)"
        [currentPage]="currentPage"
        [totalpage]="totalpage"
      ></div>

      <!-- SEO Content Section -->
    </div>
    <div app-recent-read class="row-start-1 xl:row-start-auto"></div>
    <div class="flex flex-col gap-4">
      <div app-top-list></div>
      <div *ngIf="!ssr()" app-top-users class="mb-4 xl:mb-0"></div>
    </div>
  </div>

  <div class="mt-6 p-4">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
        MeTruyenMoi - Website Đọc Truyện Tranh Online Hàng Đầu Việt Nam
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
        Khám phá thế giới truyện tranh đầy màu sắc với kho tàng khổng lồ gồm manga Nhật Bản, manhwa
        Hàn Quốc, manhua Trung Quốc hoàn toàn miễn phí.
      </p>
    </div>

    <!-- Content Preview with Height Animation -->
    <div class="relative">
      <div class="seo-content-container" [class.expanded]="isContentExpanded">
        <div class="prose prose-gray dark:prose-invert max-w-none">
          <div class="space-y-6 text-gray-700 dark:text-gray-300">
            <!-- Section 1 -->
            <div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-3 flex items-center"
              >
                MeTruyenMoi - Điểm Đến Tin Cậy Của Mọi Tín Đồ Truyện Tranh
              </h3>
              <p class="leading-relaxed">
                <strong>MeTruyenMoi</strong> tự hào là một trong những nền tảng đọc truyện tranh
                online hàng đầu tại Việt Nam. Với cam kết mang
                đến trải nghiệm đọc truyện tốt nhất, chúng tôi không ngừng cập nhật những bộ truyện
                hot nhất từ khắp châu Á, từ những siêu phẩm manga kinh điển đến các manhwa, manhua
                đang gây sốt.
              </p>
            </div>

            <!-- Section 2 -->
            <div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-3 flex items-center"
              >
                Kho Truyện Đa Dạng - Cập Nhật Liên Tục 24/7
              </h3>
              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-2">Manga Nhật Bản</h4>
                  <p class="text-sm">
                    Từ One Piece, Naruto, Attack on Titan đến những bộ truyện mới nhất như Jujutsu
                    Kaisen, Demon Slayer. Tất cả đều được cập nhật nhanh chóng với chất lượng hình
                    ảnh cao.
                  </p>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-2">Manhwa Hàn Quốc</h4>
                  <p class="text-sm">
                    Khám phá thế giới manhwa với Solo Leveling, Tower of God, The God of High School
                    và hàng trăm tựa truyện hấp dẫn khác với phong cách nghệ thuật độc đáo.
                  </p>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-2">Manhua Trung Quốc</h4>
                  <p class="text-sm">
                    Các bộ Võ luyện đỉnh phong, phàm nhân tu tiên, tiên nghịch và nhiều series tu
                    tiên, huyền huyễn đầy kịch tính.
                  </p>
                </div>
              </div>
            </div>


            <!-- Section 4 - Extended Content -->
            <div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-3 flex items-center"
              >
                Vì Sao Chọn MeTruyenMoi?
              </h3>
              <div class="space-y-4">
                <p>
                  <strong>1. Chất lượng hàng đầu:</strong> Tất cả truyện tranh đều được scan và dịch
                  thuật chất lượng cao, đảm bảo độc giả có trải nghiệm đọc tốt nhất. Hình ảnh sắc
                  nét, font chữ dễ đọc, bố cục chuyên nghiệp
                </p>
                <p>
                  <strong>2. Cập nhật nhanh nhất:</strong> Hệ thống làm việc 24/7 để mang đến những chapter mới nhất sớm nhất có thể. Nhiều
                  series được cập nhật chỉ vài giờ sau bản gốc. Hệ thống thông báo tự động sẽ báo
                  cho bạn ngay khi có chapter mới của truyện yêu thích.
                </p>
                <p>
                  <strong>3. Hoàn toàn miễn phí:</strong> Không phí đăng ký, không phí premium,
                  không giới hạn lượt đọc. MeTruyenMoi cam kết mang đến trải nghiệm đọc truyện miễn
                  phí cho tất cả mọi người. Chúng tôi tin rằng truyện tranh hay nên được mọi người
                  tiếp cận một cách dễ dàng và thoải mái nhất.
                </p>
                <p>
                  <strong>4. An toàn và bảo mật:</strong> Website được bảo vệ bởi công nghệ bảo mật
                  tiên tiến, đảm bảo thông tin cá nhân của người dùng được an toàn tuyệt đối. Hệ
                  thống SSL/TLS mã hóa mọi dữ liệu truyền tải, bảo vệ quyền riêng tư của bạn khi
                  duyệt web.
                </p>
              </div>
            </div>

            <!-- Section 5 - Extended Content -->
            <div>
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Thể Loại Truyện Đa Dạng Cho Mọi Sở Thích
              </h3>
              <div class="grid md:grid-cols-3 gap-4 text-sm mb-6">
                <div class="space-y-2">
                  <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                    <li>• Action - Hành động</li>
                    <li>• Adventure - Phiêu lưu</li>
                    <li>• Fantasy - Viễn tưởng</li>
                    <li>• Supernatural - Siêu nhiên</li>
                    <li>• Isekai - Chuyển sinh</li>
                    <li>• Battle - Chiến đấu</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                    <li>• Romance - Lãng mạn</li>
                    <li>• School Life - Học đường</li>
                    <li>• Slice of Life - Đời thường</li>
                    <li>• Drama - Tâm lý</li>
                    <li>• Josei - Phụ nữ trưởng thành</li>
                    <li>• Shoujo - Thiếu nữ</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                    <li>• Horror - Kinh dị</li>
                    <li>• Mystery - Trinh thám</li>
                    <li>• Comedy - Hài hước</li>
                    <li>• Historical - Lịch sử</li>
                    <li>• Psychological - Tâm lý học</li>
                    <li>• Sci-Fi - Khoa học viễn tưởng</li>
                  </ul>
                </div>
              </div>
              <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                Với hệ thống phân loại thông minh, bạn có thể dễ dàng tìm kiếm truyện theo sở thích
                cá nhân. Mỗi thể loại đều có những đặc trưng riêng, từ những câu chuyện hành động
                kịch tính đến những mẩu chuyện tình yêu ngọt ngào, hay những tác phẩm trinh thám đầy
                bí ẩn.
              </p>
            </div>

            <!-- Section 7 - Technology & Performance -->
            <div>
              <h3
                class="text-xl font-semibold text-gray-900 dark:text-white mb-3 flex items-center"
              >
                Công Nghệ Tiên Tiến - Hiệu Suất Vượt Trội
              </h3>
              <div class="space-y-4">
                <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                  <strong>Tối ưu hóa cho mọi thiết bị:</strong> Website được thiết kế responsive
                  hoàn hảo, tự động điều chỉnh giao diện phù hợp với màn hình điện thoại, tablet và
                  máy tính. Trải nghiệm đọc truyện mượt mà trên mọi platform, từ iOS, Android đến
                  Windows, macOS.
                </p>
                <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                  <strong>Tốc độ tải siêu nhanh:</strong> Hệ thống CDN toàn cầu giúp tải hình ảnh
                  với tốc độ lightning-fast, giảm thiểu thời gian chờ đợi. Công nghệ nén ảnh thông
                  minh đảm bảo chất lượng cao nhưng dung lượng tối ưu, tiết kiệm data cho người dùng
                  mobile.
                </p>
                <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                  <strong>Chế độ đọc offline:</strong> Tính năng cache thông minh cho phép lưu trữ
                  các chapter đã đọc để xem offline, hoàn hảo cho những chuyến đi dài hoặc khi kết
                  nối internet không ổn định.
                </p>
              </div>
            </div>

            <!-- CTA Section -->
            <div
              class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 text-center"
            >
              <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                Bắt Đầu Hành Trình Khám Phá Ngay Hôm Nay!
              </h3>
              <p class="text-gray-600 dark:text-gray-300 mb-4">
                Hãy tham gia cộng đồng các độc giả đang yêu thích MeTruyenMoi. Khám phá, đọc
                và thưởng thức những bộ truyện tranh hay nhất hoàn toàn miễn phí! Đăng ký tài khoản
                để nhận được trải nghiệm cá nhân hóa và không bỏ lỡ bất kỳ update nào.
              </p>
              <div class="flex flex-wrap justify-center gap-2 text-sm mb-4">
                <span
                  class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
                >
                  #TruyenTranh
                </span>
                <span
                  class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full"
                >
                  #Manga
                </span>
                <span
                  class="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full"
                >
                  #Manhwa
                </span>
                <span
                  class="px-3 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-full"
                >
                  #MeTruyenMoi
                </span>
                <span
                  class="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full"
                >
                  #DocTruyenMienPhi
                </span>
                <span
                  class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full"
                >
                  #CommunityFirst
                </span>
              </div>
            </div>
          </div>

          <!-- Fade Overlay when content is collapsed -->
          <div
            class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white via-white/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 pointer-events-none transition-opacity duration-300"
            [class.opacity-0]="isContentExpanded"
          ></div>
        </div>

        <!-- Read More Button -->
        <div
          *ngIf="!isContentExpanded"
          class="absolute bottom-0 left-1/2 -translate-x-1/2 text-center mt-6"
        >
          <button
            (click)="toggleContent()"
            class="inline-flex items-center px-6 py-3 text-blue-600 dark:text-blue-400 hover:shadow-md"
          >
            <span>Đọc thêm</span>
            <svg
              class="w-4 h-4 ml-2 transition-transform duration-200"
              [class.rotate-180]="isContentExpanded"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
