import { SocialAuthService } from '@abacritt/angularx-social-login';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';
import { first } from 'rxjs/operators';

@Component({
  selector: '[app-login]',
  templateUrl: './login-form.component.html',
  styleUrl: './login-form.component.scss',
  standalone: false
})
export class LoginFormComponent implements OnDestroy, OnInit {
  form!: FormGroup;
  loading = false;
  submitted = false;
  showPassword = false;

  constructor(
    private router: Router, // private authService: AuthService
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private accountService: AccountService,
    private toastService: ToastService,
    private authService: SocialAuthService,
  ) {
    this.form = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
      remember: [false],
    });
  }

  ngOnInit() {
    const rememberMeData = this.accountService.GetRememberMeData()
    if (rememberMeData) {
      // console.log('rememberMeData', rememberMeData);

      const { remember, email, password } = rememberMeData;
      this.form.controls['remember'].setValue(remember);
      if (remember) {
        this.form.patchValue({
          // remember: remember || false,  
          email: email || '',
          password: password || ''
        });
      }
    }
    this.authService.authState.subscribe((user) => {
      const userlogin = {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        photoUrl: user.photoUrl,
      };
      this.accountService.LoginWithSocial(userlogin).subscribe((res: any) => {
        if (res.status === 1) {
          this.submitted = false;
          this.accountService.SaveUser(res.data);
          this.router.navigate(['/']);
        } else {
          this.toastService.show(ToastType.Error, res.message);
        }
      });
    });
  }

  ngOnDestroy(): void { }
  onSubmit() {
    if (!this.form.valid) {
      this.submitted = true;
      return;
    }
    const email = this.form.value.email;
    const password = this.form.value.password;
    this.accountService
      .Login(email, password)
      .pipe(first())
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.submitted = false;

          const rememberMe = {
            remember: this.form.value.remember,
            email: this.form.value.remember ? email : '',
            password: this.form.value.remember ? password : '',
            // userId:res.data.id
          }
          this.accountService.SetRememberMeData(rememberMe)
          if (res.data.status === 1) {
            this.accountService.SaveUser(res.data);
            window.location.href = '/';


          } else if (res.data.status === 0) {
            // this.accountService.SaveUserDeconfirm({
            //   id: res.data.id,
            //   email: email,
            // });
            // this.router.navigate(['auth/confirm-email']);
            // this.router.navigate(['auth/login']);
            window.location.reload();

          }
        } else {
          this.submitted = true;
          this.toastService.show(ToastType.Error, res.message);
        }
      });
  }

  isControlInvalid(control: string): boolean {
    const ctrl = this.form.get(control);
    return ctrl
      ? (ctrl.invalid && (ctrl.dirty || ctrl.touched)) || this.submitted
      : false;
  }
}
