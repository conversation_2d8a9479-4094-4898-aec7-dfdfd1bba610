/* Carousel Component Styles */

.carouselContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: rgb(229 231 235);
}

:global(.dark) .emptyState {
  background-color: rgb(55 65 81);
}

.emptyState p {
  color: rgb(107 114 128);
}

:global(.dark) .emptyState p {
  color: rgb(156 163 175);
}

.carouselWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.carouselTrack {
  position: relative;
  width: 100%;
  height: 100%;
}

.carouselSlide {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

.carouselSlide:not(.active) {
  pointer-events: none;
}

.slideContent {
  position: relative;
  width: 100%;
  height: 100%;
}

.slideImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slideOverlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to top, rgb(0 0 0 / 0.6), transparent, transparent);
  display: flex;
  align-items: flex-end;
}

.slideInfo {
  padding: 1.5rem;
  color: white;
}

.slideTitle {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.slideDescription {
  font-size: 0.875rem;
  line-height: 1.25rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.slideLink {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgb(37 99 235);
  color: white;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: background-color 150ms;
}

.slideLink:hover {
  background-color: rgb(29 78 216);
}

/* Navigation Arrows */
.carouselArrow {
  @apply absolute top-1/2 -translate-y-1/2 z-10;
  @apply w-12 h-12 bg-black/50 hover:bg-black/70;
  @apply text-white rounded-full flex items-center justify-center;
  @apply transition-all duration-200 opacity-0 hover:opacity-100;
}

.carouselContainer:hover .carouselArrow {
  @apply opacity-100;
}

.prevArrow {
  @apply left-4;
}

.nextArrow {
  @apply right-4;
}

.carouselArrow svg {
  @apply w-6 h-6 stroke-2;
}

/* Play/Pause Button */
.playPauseButton {
  @apply absolute top-4 right-4 z-10;
  @apply w-10 h-10 bg-black/50 hover:bg-black/70;
  @apply text-white rounded-full flex items-center justify-center;
  @apply transition-all duration-200 opacity-0 hover:opacity-100;
}

.carouselContainer:hover .playPauseButton {
  @apply opacity-100;
}

.playPauseButton svg {
  @apply w-5 h-5;
}

/* Dots Indicator */
.carouselDots {
  @apply absolute bottom-4 left-1/2 -translate-x-1/2 z-10;
  @apply flex space-x-2;
}

.dot {
  @apply w-3 h-3 bg-white/50 hover:bg-white/80 rounded-full;
  @apply transition-all duration-200;
}

.activeDot {
  @apply bg-white scale-125;
}

/* Line Clamp Utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .slideTitle {
    @apply text-xl;
  }
  
  .slideDescription {
    @apply text-xs;
  }
  
  .slideInfo {
    @apply p-4;
  }
  
  .carouselArrow {
    @apply w-10 h-10;
  }
  
  .carouselArrow svg {
    @apply w-5 h-5;
  }
  
  .prevArrow {
    @apply left-2;
  }
  
  .nextArrow {
    @apply right-2;
  }
  
  .playPauseButton {
    @apply w-8 h-8 top-2 right-2;
  }
  
  .playPauseButton svg {
    @apply w-4 h-4;
  }
  
  .carouselDots {
    @apply bottom-2;
  }
  
  .dot {
    @apply w-2 h-2;
  }
}
