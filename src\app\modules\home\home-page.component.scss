
.home-content {
  @apply md:container w-full mx-auto;
}

.carousel-landing {
  @apply mt-0 mb-0 flex w-full h-full pb-48 xs:pb-56 sm:pb-60 lg:pb-[18%] relative;
}
.block-title
{
  @apply text-xl uppercase font-extrabold text-white flex min-h-full px-2 text-center items-center justify-center mt-1;
}

// SEO Content styles
.line-clamp-6 {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// SEO Content Container with height animation
.seo-content-container {
  height: 400px; // Initial collapsed height
  overflow: hidden;
  transition: max-height 0.5s ease-in-out;
  
  &.expanded {
    height: 100%; // Large enough to fit all content
  }
}

// Smooth fade overlay effect
.fade-overlay {
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.9) 30%,
    rgba(255, 255, 255, 0.7) 60%,
    rgba(255, 255, 255, 0.3) 80%,
    rgba(255, 255, 255, 0) 100%
  );
  
  .dark & {
    background: linear-gradient(
      to top,
      rgba(17, 24, 39, 1) 0%,
      rgba(17, 24, 39, 0.9) 30%,
      rgba(17, 24, 39, 0.7) 60%,
      rgba(17, 24, 39, 0.3) 80%,
      rgba(17, 24, 39, 0) 100%
    );
  }
  
  // Hide overlay when expanded
  &.hidden {
    opacity: 0;
    transition: opacity 0.3s ease-out;
  }
}