/* Home Page Styles */
.homeContent {
  @apply md:container w-full mx-auto;
}

.carouselLanding {
  @apply mt-0 mb-0 flex w-full h-full pb-48 xs:pb-56 sm:pb-60 lg:pb-[18%] relative;
}

.blockTitle {
  @apply text-xl uppercase font-extrabold text-white flex min-h-full px-2 text-center items-center justify-center mt-1;
}

/* SEO Content styles */
.lineClamp6 {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* SEO Content Container with height animation */
.seoContentContainer {
  height: 400px; /* Initial collapsed height */
  overflow: hidden;
  transition: max-height 0.5s ease-in-out;
}

.seoContentContainer.expanded {
  height: 100%; /* Large enough to fit all content */
}

/* Announcements Container */
.announcementsContainer {
  @apply space-y-1 w-full mt-2;
}

/* Announcement Card */
.announcementCard {
  @apply relative flex items-center px-1 bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-sm hover:shadow-lg overflow-hidden;
}

.announcementIconBg {
  @apply flex items-center justify-center w-8 h-8 rounded-full relative z-10;
}

.announcementIcon {
  @apply w-5 h-5 text-blue-600 dark:text-blue-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.announcementPulse {
  @apply absolute inset-0 w-10 h-10 bg-blue-600 dark:bg-blue-400 rounded-full animate-ping opacity-30;
}

.announcementContent {
  @apply flex-1 px-3 py-2;
}

.announcementText {
  @apply text-sm text-gray-700 dark:text-gray-300 font-medium;
}

/* Responsive Design */
@media (max-width: 768px) {
  .carouselLanding {
    @apply pb-40;
  }
  
  .blockTitle {
    @apply text-lg px-1;
  }
  
  .announcementCard {
    @apply px-2;
  }
  
  .announcementText {
    @apply text-xs;
  }
}
