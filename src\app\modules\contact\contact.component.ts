import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

interface FAQ {
  question: string;
  answer: string;
  isOpen: boolean;
}

@Component({
  selector: '[app-contact]',
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss',
  standalone: false
  , changeDetection: ChangeDetectionStrategy.OnPush
})
export class ContactComponent implements OnInit, OnDestroy {
  host = '';
  siteName = 'MeTruyenMoi';
  contactForm!: FormGroup;
  selectedFiles: File[] = [];
  isDragOver = false;
  isSubmitting = false;
  showSuccessModal = false;

  faqs: FAQ[] = [
    {
      question: 'Làm thế nào để đăng ký tài khoản?',
      answer: '<PERSON><PERSON><PERSON> có thể đăng ký tài khoản bằng cách click vào nút "Đăng ký" ở góc trên bên phải của trang web, sau đó điền thông tin email và mật khẩu.',
      isOpen: false
    },
    {
      question: 'Tôi quên mật khẩu, làm sao để khôi phục?',
      answer: 'Tại trang đăng nhập, click vào "Quên mật khẩu" và nhập email đã đăng ký. Chúng tôi sẽ gửi link khôi phục mật khẩu đến email của bạn.',
      isOpen: false
    },
    {
      question: 'Website có miễn phí không?',
      answer: 'Có, tất cả các tính năng cơ bản của website đều hoàn toàn miễn phí. Bạn có thể đọc truyện, tạo danh sách yêu thích và tương tác với cộng đồng mà không mất phí.',
      isOpen: false
    },
    {
      question: 'Làm thế nào để báo cáo nội dung vi phạm?',
      answer: 'Bạn có thể báo cáo nội dung vi phạm bằng cách gửi email đến <EMAIL> hoặc sử dụng form liên hệ với chủ đề "Báo cáo nội dung".',
      isOpen: false
    },
    {
      question: 'Tôi là chủ sở hữu bản quyền, làm sao để yêu cầu gỡ nội dung?',
      answer: 'Vui lòng gửi email đến <EMAIL> với đầy đủ thông tin chứng minh quyền sở hữu và nội dung cần gỡ bỏ.',
      isOpen: false
    },
    {
      question: 'Website có ứng dụng mobile không?',
      answer: 'Hiện tại chúng tôi chưa có ứng dụng mobile riêng, nhưng website được tối ưu hóa tốt cho thiết bị di động và có thể sử dụng mượt mà trên mọi thiết bị.',
      isOpen: false
    }
  ];

  constructor(
    private fb: FormBuilder,
    private urlService: UrlService,
    private seoService: SeoService
  ) {
    this.host = this.urlService.baseUrl;
    this.initializeForm();
  }

  ngOnInit(): void {
    this.setupSEO();
    this.setupDragAndDrop();
  }

  ngOnDestroy(): void {
    this.removeDragAndDropListeners();
  }

  private initializeForm(): void {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      subject: ['', Validators.required],
      message: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(1000)]],
      agreePrivacy: [false, Validators.requiredTrue]
    });
  }

  private setupSEO(): void {
    this.seoService.setTitle('Liên hệ - Hỗ trợ và tư vấn');
    this.seoService.addTags([
      { name: 'description', content: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi. Chúng tôi sẵn sàng giải đáp mọi thắc mắc và hỗ trợ bạn 24/7. Phản hồi nhanh chóng và chuyên nghiệp.' },
      { name: 'robots', content: 'index, follow' },
      { name: 'author', content: 'MeTruyenMoi' },
      { property: 'og:description', content: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi - Phản hồi nhanh chóng và chuyên nghiệp' },
      { property: 'og:title', content: 'Liên hệ - MeTruyenMoi' },
      { property: 'og:url', content: `${this.host}/lien-he` },
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { property: 'og:locale', content: 'vi_VN' },
      { itemprop: 'name', content: 'Liên hệ' },
      { itemprop: 'description', content: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi' },
      { name: 'twitter:card', content: 'summary' },
      { name: 'twitter:title', content: 'Liên hệ - MeTruyenMoi' },
      { name: 'twitter:description', content: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi - Phản hồi nhanh chóng và chuyên nghiệp' }
    ]);
    this.seoService.updateLink('canonical', `${this.host}/lien-he`);
  }

  private setupDragAndDrop(): void {
    if (typeof window !== 'undefined') {
      document.addEventListener('dragover', this.onDragOver.bind(this));
      document.addEventListener('dragleave', this.onDragLeave.bind(this));
      document.addEventListener('drop', this.onDrop.bind(this));
    }
  }

  private removeDragAndDropListeners(): void {
    if (typeof window !== 'undefined') {
      document.removeEventListener('dragover', this.onDragOver.bind(this));
      document.removeEventListener('dragleave', this.onDragLeave.bind(this));
      document.removeEventListener('drop', this.onDrop.bind(this));
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.addFiles(Array.from(input.files));
    }
  }

  private addFiles(files: File[]): void {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    files.forEach(file => {
      if (file.size > maxSize) {
        alert(`File ${file.name} quá lớn. Kích thước tối đa là 10MB.`);
        return;
      }

      if (!allowedTypes.includes(file.type)) {
        alert(`File ${file.name} không được hỗ trợ. Chỉ chấp nhận JPG, PNG, GIF, PDF, DOC.`);
        return;
      }

      if (this.selectedFiles.length < 5) {
        this.selectedFiles.push(file);
      } else {
        alert('Chỉ có thể đính kèm tối đa 5 file.');
      }
    });
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  private onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  private onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer?.files) {
      this.addFiles(Array.from(event.dataTransfer.files));
    }
  }

  toggleFaq(index: number): void {
    this.faqs[index].isOpen = !this.faqs[index].isOpen;
  }

  openLiveChat(): void {
    // Implement live chat functionality
    alert('Tính năng chat trực tuyến sẽ được triển khai sớm!');
  }

  openSocialMedia(): void {
    // Implement social media links
    alert('Theo dõi chúng tôi trên các mạng xã hội!');
  }

  onSubmit(): void {
    if (this.contactForm.valid) {
      this.isSubmitting = true;
      
      // Simulate API call
      setTimeout(() => {
        this.isSubmitting = false;
        this.showSuccessModal = true;
        this.contactForm.reset();
        this.selectedFiles = [];
      }, 2000);
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      control?.markAsTouched();
    });
  }

  closeSuccessModal(): void {
    this.showSuccessModal = false;
  }
}
