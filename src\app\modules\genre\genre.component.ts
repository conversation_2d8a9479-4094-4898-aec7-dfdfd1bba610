import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, computed, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicStatus, Genre, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { GenreService } from '@services/genre.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import {
  IFilters,
  genreFiltersOptions,
} from '../../components/utils/constants';

@Component({
  selector: '[app-genre]',
  templateUrl: './genre.component.html',
  styleUrl: './genre.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GenreComponent extends OptimizedBaseComponent implements OnInit {
  // Component state with signals for optimal performance
  listComics = signal<Comic[]>([]);
  currentGenre = signal<Genre | undefined>(undefined);
  genres = signal<Genre[]>([]);

  // Pagination and filters
  totalpage = signal<number>(0);
  totalResult = signal<number>(0);
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: SortType.LastUpdate, name: 'Mới cập nhật' },
    status: { value: ComicStatus.ALL, name: 'Tất cả' },
  };

  private genreSlug = '';
  isLoading = signal<boolean>(false);

  // Computed properties for optimal performance
  hasComics = computed(() => this.listComics().length > 0);
  totalComicsCount = computed(() => this.totalpage() * 35);
  genreDescription = computed(() => {
    const genre = this.currentGenre();
    if (!genre) return '';
    return genre.description ||
      `Khám phá kho tàng truyện tranh thể loại ${genre.title} với những câu chuyện hấp dẫn, đa dạng và phong phú. Đọc online miễn phí tại MeTruyenMoi.`;
  });

  // Performance optimizations
  private debouncedLoadComics!: Function;

  // genres: Genre[] = [];
  

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    private genreService: GenreService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
    this.dataView = {
      status: genreFiltersOptions.status,
      sorts: genreFiltersOptions.sorts,
    };
    this.setupDebouncedMethods();
  }

  private setupDebouncedMethods(): void {
    this.debouncedLoadComics = this.debounce((page: number, sort: number, status: number) => {
      this.performLoadComicsByGenre(page, sort, status);
    }, 300);
  }

  ngOnInit(): void {
    this.genreService.getGenres().pipe(this.takeUntilDestroy()).subscribe((genres: any) => {
      this.genres.set(genres);
    });
    

    // Subscribe to route params to get genre slug
    this.route.params.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      this.genreSlug = params['slug'];
      console.log(this.genreSlug)
      this.loadGenreInfo();
    });

    // Subscribe to query params for pagination and filters
    this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      const page = Number(params['page']) || 1;
      const status = Number(params['status']) >= 0 ? Number(params['status']) : ComicStatus.ALL;
      const sort = Number(params['sort']) >= 0 ? Number(params['sort']) : SortType.LastUpdate;

      this.currentPage = page;
      this.selectOptions.sorts.value = sort;
      this.selectOptions.status.value = status;

      if (this.currentGenre()) {
        this.debouncedLoadComics(page, sort, status);
      }
    });
  }

  /**
   * Load genre information
   */
  private loadGenreInfo(): void {
    if (!this.genreSlug) return;
    this.genreService.getGenreBySlug(this.genreSlug)
      .pipe(this.takeUntilDestroy())
      .subscribe((genre: any) => {
        if (!genre) {
          this.router.navigate(['/404']);
          return;
        }
        this.currentGenre.set(genre);
        this.setupSeo();

        // Load comics after genre info is loaded
        const page = this.currentPage;
        const sort = this.selectOptions.sorts.value;
        const status = this.selectOptions.status.value;
        this.debouncedLoadComics(page, sort, status);

        this.safeMarkForCheck();
      });
  }

  /**
   * Load comics by genre with filters - debounced for performance
   */
  private performLoadComicsByGenre(page: number, sort: number, status: number): void {
    if (!this.currentGenre() || this.isLoading()) return;

    this.isLoading.set(true);
    this.comicService
      .getComics({
        step: '35',
        genre: this.currentGenre()!.id.toString(),
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalpage.set(res.data.totalpage);
        this.totalResult.set(res.data.totalResult || 0);
        this.listComics.set(res.data.comics || []);

        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics().length > 0) {
          this.showDetails(this.listComics()[0]);
        }

        this.isLoading.set(false);
        this.safeMarkForCheck();
      });
  }

  /**
   * Load comics by genre with filters - legacy method
   */
  private loadComicsByGenre(page: number, sort: number, status: number): void {
    this.performLoadComicsByGenre(page, sort, status);
  }

  /**
   * Handle sort option change
   */
  onSortOptionChange(value: number): void {
    this.selectOptions.sorts.value = value;
    this.router.navigate([], {
      queryParams: {
        sort: value,
        page: 1 // Reset to first page when changing sort
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Handle status option change
   */
  onStatusOptionChange(value: number): void {
    this.selectOptions.status.value = value;
    this.router.navigate([], {
      queryParams: {
        status: value,
        page: 1 // Reset to first page when changing status
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Show comic details
   */
  showDetails(comic: Comic): void {
    this.selectedComic = comic;
  }

  /**
   * Handle page change
   */
  onChangePage(page: number): void {
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Setup SEO for genre page
   */
  private setupSeo(): void {
    const genre = this.currentGenre();
    if (!genre) return;

    const title = `Truyện tranh ${genre.title}`;
    const description = genre.description ||
      `Đọc truyện tranh thể loại ${genre.title} online miễn phí. Kho tàng truyện ${genre.title} phong phú, cập nhật liên tục tại MeTruyenMoi.`;

    // Use SeoService to set comprehensive SEO
    this.seoService.setGenreSEO(genre, this.listComics(), this.currentPage);
  }

  /**
   * Track by function for comic list optimization
   */
  trackByComicId = (index: number, comic: Comic): number => comic.id;

  /**
   * Track by function for genre list optimization
   */
  trackByGenreId = (index: number, genre: Genre): number => genre.id;
}
