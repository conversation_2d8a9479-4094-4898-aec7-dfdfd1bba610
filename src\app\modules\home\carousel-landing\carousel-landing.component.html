<!-- Desktop Carousel -->
<div
  class="desktop-carousel"
  (mousedown)="onMouseDown($event)"
  (mousemove)="onMouseMove($event)"
>
  <!-- Navigation Controls -->
  <!-- <button
    *ngIf="hasCarouselItems()"
    class="nav-left"
    (click)="next()"
    aria-label="Previous carousel item"
    (keydown.enter)="next()"
    (keydown.space)="next()"
  >
    <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256" />
    </svg>
  </button> -->

  <!-- Comic Details Overlay -->
  <div *ngIf="shouldShowHoverDetails()" [@formAnimation] class="details-overlay">
    <div class="details-content">
      <div class="comic-header">
        <h3 class="comic-title">{{ hoverComic()?.title }}</h3>
        <div class="status-container">
          @if (hoverComic()?.status === 0) {
          <div class="status-ongoing"></div>
          <span>Đang tiến hành</span>
          } @else {
          <svg class="status-completed" viewBox="0 0 512 512">
            <path
              fill="#2debb2"
              d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"
            />
          </svg>
          <span>Hoàn thành</span>
          }
        </div>
      </div>

      <div class="genre-tags">
        <a *ngFor="let tag of hoverComic()?.genres; index as i" class="genre-tag">
          @if (i === 0) {
          <span class="tag-primary">{{ tag.title }}</span>
          } @else {
          <span class="tag-secondary">{{ tag.title }}</span>
          }
        </a>
      </div>

      <div class="comic-stats">
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path
              d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"
            />
          </svg>
          {{ hoverComic()?.rating }}
        </div>
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" />
          </svg>
          {{ hoverComic()?.rating }}
        </div>
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8" />
            <circle cx="12" cy="12" r="3" />
          </svg>
          {{ hoverComic()?.viewCount | numeral }}
        </div>
      </div>

      <div class="comic-description">
        <p
          class="description-text"
          [innerHTML]="
            hoverComic()?.description
              | fillDescription : hoverComic()!.id : hoverComic()!.title : hoverComic()!.url
          "
        ></p>
      </div>
    </div>
  </div>

  <!-- <button
    *ngIf="hasCarouselItems()"
    class="nav-right"
    (click)="prev()"
    aria-label="Next carousel item"
    (keydown.enter)="prev()"
    (keydown.space)="prev()"
  >
    <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256" />
    </svg>
  </button> -->

  <!-- Carousel Items -->
  <div
    class="transition-transform duration-100 ease-linear w-full h-full flex"
    [style.transform]="'translateX(' + curTransformX() + 'px)'"
    [class.cursor-grab]="isDragging"
  >
    <div
      *ngFor="let comics of carouselItems(); let i = index; trackBy: trackByCarouselIndex"
      class="carousel-item"   
      [style.width]="'calc(100% / var(--carousel-grid) )'"
      [style.left]="getLeftPosition(i)"
    >
      <a
        *ngFor="let comic of comics; let i = index; trackBy: trackByComicId"
        [routerLink]="getComicUrl(comic)"
        class="block-item"
        [title]="comic.title"
      >
        <img
          (mouseenter)="OnComicHover(comic)"
          (mouseleave)="OnComicLeave()"
          class="item-image"
          [src]="comic.coverImage || '/option2.png'"
          [alt]="comic.title"
          loading="eager"
          onerror="this.src='/option2.png'"
        />
        <div class="item-overlay">
          <h3 class="text-sm">{{ comic.title }}</h3>
        </div>
      </a>
    </div>
  </div>

  <!-- Loading State -->
  <div
    *ngIf="!hasCarouselItems()"
    class="loading-container"
    role="status"
    aria-label="Loading carousel content"
  >
    <div app-spinner [sizeSpinner]="'40'"></div>
  </div>
</div>

<!-- Swiper Component -->
<!-- <app-swiper
  *ngIf="isBrowser"
  class="swiper-container"
  (nextChange)="SlideChange($event)"
/> -->
