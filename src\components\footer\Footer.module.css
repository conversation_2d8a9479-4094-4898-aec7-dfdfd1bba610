/* Modern Comic-themed Footer Styles */
.footerContainer {
  @apply relative bg-white dark:bg-dark-bg;
  @apply text-neutral-900 dark:text-white overflow-hidden;
}

.footerDivider {
  @apply border-t border-neutral-300 dark:border-neutral-600 border-opacity-60 mt-5;
}

/* Main Content */
.footerContent {
  @apply relative z-10 max-w-7xl mx-auto px-4 py-8;
}

.footerGrid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8;
}

/* Brand Section */
.footerBrand {
  @apply space-y-4;
}

.brandLogo {
  @apply flex flex-col items-center lg:items-start gap-4;
}

.logoImage {
  @apply h-16 w-auto object-contain;
}

.brandText {
  @apply flex-1;
}

.brandName {
  @apply text-xl font-bold text-neutral-900 dark:text-white mb-1;
}

.brandTagline {
  @apply text-sm text-neutral-800 dark:text-neutral-400;
}

.brandDescription {
  @apply text-neutral-700 dark:text-neutral-300 text-sm mb-6;
}

/* Contact Section */
.contactSection {
  @apply space-y-4 mb-6;
}

.contactEmail {
  @apply text-sm text-neutral-600 dark:text-neutral-400;
}

.footerLinks {
  @apply flex gap-4;
}

.footerLink {
  @apply text-neutral-600 dark:text-neutral-400 hover:text-blue-600 dark:hover:text-blue-400;
  @apply text-sm hover:underline;
}

/* Social Media */
.socialSection {
  @apply space-y-4;
}

.socialTitle {
  @apply text-lg font-semibold text-neutral-900 dark:text-white mb-4;
}

.socialLinks {
  @apply flex gap-3;
}

.socialLink {
  @apply flex items-center gap-2 px-4 py-2;
  @apply bg-neutral-100 dark:bg-neutral-700 hover:bg-neutral-200 dark:hover:bg-neutral-600;
  @apply text-neutral-700 dark:text-neutral-300;
  @apply rounded-lg text-sm;
  @apply hover:scale-105 hover:shadow-lg transition-all duration-200;
}

.socialLink.facebook {
  @apply hover:bg-sky-100 dark:hover:bg-sky-600 hover:text-sky-600 dark:hover:text-white;
}

.socialLink.twitter {
  @apply hover:bg-sky-50 dark:hover:bg-sky-400 hover:text-sky-400 dark:hover:text-white;
}

.socialLink.discord {
  @apply hover:bg-indigo-100 dark:hover:bg-indigo-600 hover:text-indigo-600 dark:hover:text-white;
}

.socialIcon {
  @apply w-4 h-4;
}

/* Footer Social Section */
.footerSocial {
  @apply space-y-6;
}

.sectionTitle {
  @apply text-lg font-semibold text-neutral-900 dark:text-white mb-2 w-full;
}

/* Facebook Section */
.facebookSection {
  @apply mb-6;
}

/* Popular Tags */
.footerTags {
  @apply flex flex-wrap gap-2;
}

.tagsContainer {
  @apply flex flex-wrap gap-2;
}

.tagItem {
  @apply px-3 py-1 bg-neutral-100 dark:bg-neutral-700 hover:bg-blue-600 dark:hover:bg-blue-600;
  @apply text-neutral-700 dark:text-neutral-300 hover:text-white dark:hover:text-white;
  @apply rounded-full text-xs;
  @apply hover:scale-105 border border-neutral-300 dark:border-neutral-600 hover:border-blue-600;
  @apply transition-all duration-200;
}

/* Footer Bottom */
.footerBottom {
  @apply border-t border-neutral-200 dark:border-neutral-700 pt-8;
}

.footerBottomContent {
  @apply flex flex-col lg:flex-row justify-between items-center gap-6;
}

.copyrightSection {
  @apply text-center lg:text-left;
}

.copyrightText {
  @apply text-neutral-900 dark:text-white font-medium mb-2;
}

.disclaimerText {
  @apply text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed;
}

.footerStats {
  @apply flex gap-8;
}

.statItem {
  @apply text-center;
}

.statNumber {
  @apply block text-2xl font-bold text-blue-600 dark:text-blue-400;
}

.statLabel {
  @apply text-neutral-600 dark:text-neutral-400 text-sm;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footerGrid {
    @apply grid-cols-1 gap-6;
  }

  .brandLogo {
    @apply justify-center text-center;
  }

  .socialLinks {
    @apply justify-center;
  }

  .tagsContainer {
    @apply justify-center;
  }

  .footerStats {
    @apply gap-4;
  }

  .statNumber {
    @apply text-xl;
  }
}
