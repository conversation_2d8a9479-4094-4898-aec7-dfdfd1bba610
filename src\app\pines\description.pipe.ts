import { Pipe, PipeTransform } from '@angular/core';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';

@Pipe({
    name: 'fillDescription',
    standalone: true
})
export class ComicDescriptionPipe implements PipeTransform {
  constructor(private urlService: UrlService) { 
    // Constructor logic if needed

  }

  transform(value: string | null | undefined, id: number|undefined, title: string, url: string): string {
    if(this.urlService.domainType === 1)
    {
      id = undefined;
    }
    return ServiceUtility.fillDescription(value
      , { url: url, id: id, title: title }, true
    );

  }

}
