<!-- Simplified Reading History Component using existing components -->
<div class="reading-history-container">
  <!-- Page Header -->
  <div class="page-header">
    <h2 class="page-title">
      <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <circle cx="12" cy="12" r="10" />
        <polyline points="12,6 12,12 16,14" />
      </svg>
      L<PERSON>ch sử đọc
    </h2>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-state">
    <div app-spinner [sizeSpinner]="'40'"></div>
  </div>

  <!-- Main Content -->
  <ng-container *ngIf="!isLoading">
    <!-- Comics Grid -->
    <div
      class="grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 mx-3 lg:mx-0"
    >
      <div *ngFor="let comic of comics; let i = index">
        <div app-comic-card [comic]="comic"></div>
      </div>
    </div>

    <!-- Pagination -->
    <div
      app-pagination
      *ngIf="totalPages > 1 && isBrowser"
      [totalpage]="totalPages"
      [currentPage]="currentPage"
      (OnChange)="onPageChange($event)"
    ></div>

    <!-- Empty State -->
    <ng-template #empty>
      <div class="empty-state">
        <div app-empty [message]="'Chưa có lịch sử đọc'"></div>
        <div class="empty-actions">
          <a [routerLink]="['/']" class="explore-btn">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8" />
              <path d="M21 21l-4.35-4.35" />
            </svg>
            Khám phá truyện
          </a>
        </div>
      </div>
    </ng-template>
  </ng-container>
</div>
