import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: 'div[app-selection]',
  templateUrl: './selection.component.html',
  styleUrl: './selection.component.scss',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, ClickOutsideDirective],
})
export class SelectionComponent implements OnInit, AfterViewInit, OnChanges {
  @Output() onChange = new EventEmitter();
  @Input()
  options?: IOption[];
  @Input()
  value = 0;
  idx = 0;
  
  @ViewChild('SelectionButton') 
  selection!: ElementRef;
  isVisible = false;
  constructor(private cd : ChangeDetectorRef) { }
  ngOnInit() {
    this.isVisible = false;
    
  }
  ngAfterViewInit() {
    this.selection.nativeElement.addEventListener('click', () => {
      this.isVisible = !this.isVisible;
      this.cd.markForCheck();
    })
  }
  ClickOutside() {
    this.isVisible = false;
    this.cd.markForCheck();    
  }

  selectOption(value: number) {
    if (!this.options) return;
    this.onChange.emit(value);
    this.value = value;
    this.idx = this.options.findIndex((option) => option.value === this.value);

  }
  ngOnChanges() {
    if (!this.options) return;
    this.idx = this.options.findIndex((option) => option.value === this.value);
  }
}
