// ===== SYNC RESULTS COMPONENT =====
// Beautiful results display with comprehensive stats

// ===== CONTAINER =====
.sync-results-container {
  @apply bg-white dark:bg-neutral-800 rounded-2xl p-6 
         border border-gray-200 dark:border-neutral-700 
         shadow-lg space-y-6 transition-all duration-500;

  &.success {
    @apply border-green-300 dark:border-green-700 shadow-green-100 dark:shadow-green-900/20;
  }

  &.error {
    @apply border-red-300 dark:border-red-700 shadow-red-100 dark:shadow-red-900/20;
  }
}

// ===== RESULTS HEADER =====
.results-header {
  @apply flex items-center gap-4;
}

.header-icon {
  @apply w-16 h-16 rounded-2xl flex items-center justify-center;

  &.success {
    @apply bg-green-100 dark:bg-green-900/30;
  }

  &.error {
    @apply bg-red-100 dark:bg-red-900/30;
  }
}

.icon {
  @apply w-8 h-8;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.success {
    @apply text-green-600 dark:text-green-400;
  }

  &.error {
    @apply text-red-600 dark:text-red-400;
  }
}

.header-content {
  @apply flex-1 space-y-1;
}

.header-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.header-subtitle {
  @apply text-gray-600 dark:text-gray-400;
}

.success-rate {
  @apply flex items-center;
}

.rate-circle {
  @apply w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/30 
         flex items-center justify-center;
}

.rate-text {
  @apply text-lg font-bold text-green-600 dark:text-green-400;
}

// ===== SUMMARY STATS =====
.summary-stats {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-4;
}

.stat-card {
  @apply p-4 rounded-xl text-center space-y-2 transition-all duration-200;
}

.stat-value {
  @apply text-2xl font-bold;
}

.stat-label {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

// ===== DETAIL STATS =====
.detail-stats {
  @apply space-y-4;
}

.detail-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.detail-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

.detail-item {
  @apply flex items-center gap-3 p-3 
         bg-gray-50 dark:bg-neutral-700 rounded-lg;
}

.detail-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
}

.detail-dot {
  @apply w-3 h-3 rounded-full;
}

.detail-content {
  @apply flex flex-col;
}

.detail-value {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.detail-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== TABS NAVIGATION =====
.tabs-navigation {
  @apply border-b border-gray-200 dark:border-neutral-700;
}

.tabs-container {
  @apply flex space-x-1;
}

.tab-button {
  @apply flex items-center gap-2 px-4 py-3 
         text-sm font-medium text-gray-600 dark:text-gray-400 
         hover:text-gray-900 dark:hover:text-white 
         border-b-2 border-transparent 
         hover:border-gray-300 dark:hover:border-neutral-600
         transition-all duration-200;

  &.active {
    @apply text-blue-600 dark:text-blue-400 border-blue-500;
  }
}

.tab-label {
  @apply font-medium;
}

.tab-count {
  @apply px-2 py-1 text-xs bg-gray-200 dark:bg-neutral-600 
         text-gray-700 dark:text-gray-300 rounded-full;

  .tab-button.active & {
    @apply bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300;
  }
}

// ===== TAB CONTENT =====
.tab-content {
  @apply min-h-64;
}

.tab-panel {
  @apply py-6;
}

// ===== SUMMARY PANEL =====
.summary-panel {
  @apply space-y-6;
}

.summary-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.summary-section {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.section-content {
  @apply space-y-3;
}

.summary-item {
  @apply flex justify-between items-center py-2 
         border-b border-gray-100 dark:border-neutral-700 last:border-b-0;
}

.item-label {
  @apply text-gray-600 dark:text-gray-400;
}

.item-value {
  @apply font-semibold text-gray-900 dark:text-white;

  &.success {
    @apply text-green-600 dark:text-green-400;
  }

  &.error {
    @apply text-red-600 dark:text-red-400;
  }

  &.new {
    @apply text-blue-600 dark:text-blue-400;
  }

  &.updated {
    @apply text-purple-600 dark:text-purple-400;
  }
}


// ===== ACTION BUTTONS =====
.results-actions {
  @apply flex flex-col sm:flex-row gap-3 justify-end pt-6 
         border-t border-gray-200 dark:border-neutral-700;
}

.action-button {
  @apply flex items-center justify-center gap-2 px-6 py-3 
         font-semibold rounded-lg 
         transition-all duration-200;

  &.primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 
           hover:from-blue-600 hover:to-purple-700 
           text-white shadow-lg hover:shadow-xl;
  }

  &.secondary {
    @apply bg-gray-100 hover:bg-gray-200 
           dark:bg-neutral-700 dark:hover:bg-neutral-600 
           text-gray-700 dark:text-gray-300 
           border border-gray-300 dark:border-neutral-600;
  }

  &.tertiary {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-neutral-800 
           text-gray-600 dark:text-gray-400 
           border border-gray-300 dark:border-neutral-600;
  }
}

.button-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.button-text {
  @apply font-medium;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .sync-results-container {
    @apply p-4 space-y-4;
  }

  .results-header {
    @apply flex-col items-start gap-3;
  }

  .summary-stats {
    @apply grid-cols-2 gap-3;
  }

  .detail-grid {
    @apply grid-cols-1 gap-3;
  }

  .summary-grid {
    @apply grid-cols-1 gap-4;
  }

  .results-actions {
    @apply flex-col;
  }

  .action-button {
    @apply w-full;
  }
}
