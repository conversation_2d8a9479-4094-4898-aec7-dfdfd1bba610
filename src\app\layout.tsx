import type { Metadata } from 'next';
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'MeTruyenMoi',
  description: 'Website đọc truyện tranh online miễn phí',
  applicationName: 'MeTruyenMoi',
  robots: 'index, follow',
  themeColor: '#E83A3A',
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    siteName: 'MeTruyenMoi',
    locale: 'vi_VN',
    images: [
      {
        url: 'https://metruyenmoi.com/logo.png',
        width: 339,
        height: 160,
        alt: 'MeTruyenMoi - Website đọc truyện tranh online miễn phí',
      },
    ],
  },
  other: {
    copyright: 'Copyright © 2025 MeTruyenMoi',
    formatDetection: 'telephone=no',
    msapplicationTileColor: '#E83A3A',
    mobileWebAppCapable: 'yes',
    mobileWebAppStatusBarStyle: 'default',
    mobileWebAppTitle: 'MeTruyenMoi',
    google: 'notranslate',
    // Add more custom meta tags here if needed
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Giả sử currentTheme và isBrowser là biến JS, bạn cần truyền từ props hoặc context nếu dùng thực tế
  const currentTheme = false; // hoặc lấy từ context/provider
  const isBrowser = typeof window !== 'undefined';

  return (
    <html lang='vi'>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div
          className={`${
            currentTheme ? 'dark' : 'light'
          } bg-white dark:bg-dark-bg dark:text-white`}
        >
          {/* Thay thế <div app-nav></div> bằng component Nav nếu có */}
          <main className='main'>{children}</main>
          {/* Thay thế <footer app-footer></footer> bằng component Footer nếu có */}
          {/* <Footer /> */}
          {/* Hiển thị chat box nếu là browser */}

          <div className='chat-box-bubble'></div>

          <div id='popupContainer'></div>
        </div>
      </body>
    </html>
  );
}
