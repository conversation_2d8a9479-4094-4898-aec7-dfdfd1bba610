'use client';

import { useState, useEffect } from 'react';
import styles from './Carousel.module.css';

interface CarouselItem {
  id: number;
  title: string;
  image: string;
  description?: string;
  link?: string;
}

interface CarouselProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
}

export default function Carousel({
  items,
  autoPlay = true,
  autoPlayInterval = 5000,
  showDots = true,
  showArrows = true
}: CarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  useEffect(() => {
    if (!isPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === items.length - 1 ? 0 : prevIndex + 1
      );
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, items.length, autoPlayInterval]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? items.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === items.length - 1 ? 0 : currentIndex + 1);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  if (items.length === 0) {
    return (
      <div className={styles.carouselContainer}>
        <div className={styles.emptyState}>
          <p>Không có nội dung để hiển thị</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.carouselContainer}>
      <div className={styles.carouselWrapper}>
        {/* Main Carousel */}
        <div className={styles.carouselTrack}>
          {items.map((item, index) => (
            <div
              key={item.id}
              className={`${styles.carouselSlide} ${
                index === currentIndex ? styles.active : ''
              }`}
              style={{
                transform: `translateX(${(index - currentIndex) * 100}%)`
              }}
            >
              <div className={styles.slideContent}>
                <img
                  src={item.image}
                  alt={item.title}
                  className={styles.slideImage}
                  loading={index === currentIndex ? 'eager' : 'lazy'}
                />
                <div className={styles.slideOverlay}>
                  <div className={styles.slideInfo}>
                    <h3 className={styles.slideTitle}>{item.title}</h3>
                    {item.description && (
                      <p className={styles.slideDescription}>{item.description}</p>
                    )}
                    {item.link && (
                      <a href={item.link} className={styles.slideLink}>
                        Xem chi tiết
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        {showArrows && items.length > 1 && (
          <>
            <button
              className={`${styles.carouselArrow} ${styles.prevArrow}`}
              onClick={goToPrevious}
              aria-label="Slide trước"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="15,18 9,12 15,6"></polyline>
              </svg>
            </button>
            <button
              className={`${styles.carouselArrow} ${styles.nextArrow}`}
              onClick={goToNext}
              aria-label="Slide tiếp theo"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </button>
          </>
        )}

        {/* Play/Pause Button */}
        {autoPlay && items.length > 1 && (
          <button
            className={styles.playPauseButton}
            onClick={togglePlayPause}
            aria-label={isPlaying ? 'Tạm dừng' : 'Phát'}
          >
            {isPlaying ? (
              <svg viewBox="0 0 24 24" fill="currentColor">
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
            ) : (
              <svg viewBox="0 0 24 24" fill="currentColor">
                <polygon points="5,3 19,12 5,21"></polygon>
              </svg>
            )}
          </button>
        )}
      </div>

      {/* Dots Indicator */}
      {showDots && items.length > 1 && (
        <div className={styles.carouselDots}>
          {items.map((_, index) => (
            <button
              key={index}
              className={`${styles.dot} ${
                index === currentIndex ? styles.activeDot : ''
              }`}
              onClick={() => goToSlide(index)}
              aria-label={`Đi đến slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
