import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DeviceService {
  private userAgent: string;

  constructor() {
    this.userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
  }

  isMobile(): boolean {
    return /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent.toLowerCase());
  }

  isTablet(): boolean {
    return /ipad|tablet|playbook|silk/i.test(this.userAgent.toLowerCase()) && !this.isMobile();
  }

  isDesktop(): boolean {
    return !this.isMobile() && !this.isTablet();
  }

  getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    if (this.isMobile()) return 'mobile';
    if (this.isTablet()) return 'tablet';
    return 'desktop';
  }
}
