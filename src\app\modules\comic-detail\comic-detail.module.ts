import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ComicDetailComponent } from './comic-detail.component';
import { ChapterListComponent } from './page/list-chapter/chapter-list.component';
// import { TopListComponent } from './page/top-list/top-list.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { CommentComponent } from '@components/common/comment/comment.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { TopUsersComponent } from '@components/common/top-users/top-users.component';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { ComicResolver } from 'src/app/resolvers/comic.resolver';
import { AdsModule } from 'src/app/shared/ads.module';

@NgModule({
    declarations: [
        ComicDetailComponent,
    ], imports: [RouterModule.forChild([{
        path: '', component: ComicDetailComponent, resolve: { comicRes: ComicResolver }
    }]),
        CommonModule,
        ReactiveFormsModule,
        AdsModule,
        ChapterListComponent,
        TopListComponent,
        CommentComponent,
        ComicDescriptionPipe,
        DateAgoPipe,
        NumeralPipe,
        BreadcrumbComponent,
        GridComicComponent,
        TopUsersComponent
    ], providers: [provideHttpClient(withInterceptorsFromDi())]
})
export class DetailModule { }
