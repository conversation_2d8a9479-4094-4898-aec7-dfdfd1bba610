// ===== AUTHOR COMICS PAGE STYLES =====
// Modern, responsive design for author comics listing

// ===== MAIN CONTAINER =====
.author-comics-container {
  @apply min-h-screen bg-white dark:bg-neutral-900;
}

// ===== BREADCRUMB SECTION =====
.breadcrumb-section {
  @apply bg-neutral-50 dark:bg-neutral-800/50 border-b border-neutral-200 dark:border-neutral-700;
  @apply py-3 px-4 sm:px-6 lg:px-8;
}

// ===== PAGE HEADER =====
.page-header {
  @apply bg-gradient-to-r from-primary-50 to-primary-100 dark:from-neutral-800 dark:to-neutral-700;
  @apply border-b border-neutral-200 dark:border-neutral-600;
}

.header-content {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
}

.author-info {
  @apply flex items-start gap-6;
}

.author-avatar {
  @apply w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200;
  @apply rounded-full flex items-center justify-center shadow-lg;
  @apply ring-4 ring-white dark:ring-neutral-700;
}

.avatar-icon {
  @apply w-10 h-10 text-white stroke-2;
}

.author-details {
  @apply flex-1;
}

.author-name {
  @apply text-3xl font-bold text-neutral-900 dark:text-white mb-2;
  @apply bg-gradient-to-r from-primary-100 to-primary-200;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.author-subtitle {
  @apply text-lg text-neutral-600 dark:text-neutral-300 mb-4;
}

.author-stats {
  @apply flex gap-6;
}

.stat-item {
  @apply flex items-center gap-2 px-4 py-2 rounded-lg;
  @apply bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm;
  @apply border border-neutral-200 dark:border-neutral-600;
}

.stat-icon {
  @apply w-5 h-5 text-primary-100 stroke-2;
}

.stat-value {
  @apply text-xl font-bold text-neutral-900 dark:text-white;
}

.stat-label {
  @apply text-sm text-neutral-600 dark:text-neutral-400;
}

// ===== CONTENT SECTION =====
.content-section {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
}

// ===== LOADING STATE =====
.loading-container {
  @apply flex flex-col items-center justify-center py-16;
}

.loading-text {
  @apply mt-4 text-neutral-600 dark:text-neutral-400 text-lg;
}

// ===== ERROR STATE =====
.error-container {
  @apply flex items-center justify-center py-16;
}

.error-content {
  @apply text-center max-w-md;
}

.error-icon {
  @apply w-16 h-16 text-red-500 mx-auto mb-4 stroke-2;
}

.error-title {
  @apply text-xl font-semibold text-neutral-900 dark:text-white mb-2;
}

.error-message {
  @apply text-neutral-600 dark:text-neutral-400 mb-6;
}

.retry-btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg;
  @apply bg-primary-100 hover:bg-primary-200 text-white;
  @apply transition-colors duration-200 font-medium;
}

.btn-icon {
  @apply w-4 h-4 stroke-2;
}

// ===== COMICS SECTION =====
.comics-section {
  @apply space-y-6;
}

.section-header {
  @apply flex items-center justify-between flex-wrap gap-4;
  @apply pb-4 border-b border-neutral-200 dark:border-neutral-700;
}

.section-title {
  @apply flex items-center gap-3 text-2xl font-bold;
  @apply text-neutral-900 dark:text-white;
}

.title-icon {
  @apply w-6 h-6 text-primary-100 stroke-2;
}

.result-count {
  @apply flex items-center gap-2 px-4 py-2 rounded-lg;
  @apply bg-neutral-100 dark:bg-neutral-800;
}

.count-number {
  @apply text-lg font-bold text-primary-100;
}

.count-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400;
}

// ===== COMICS GRID =====
.comics-grid {
  @apply mt-6;
}

// ===== EMPTY STATE =====
.empty-container {
  @apply py-16;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 640px) {
  .header-content {
    @apply py-6;
  }
  
  .author-info {
    @apply flex-col text-center gap-4;
  }
  
  .author-avatar {
    @apply w-16 h-16 mx-auto;
  }
  
  .avatar-icon {
    @apply w-8 h-8;
  }
  
  .author-name {
    @apply text-2xl;
  }
  
  .author-subtitle {
    @apply text-base;
  }
  
  .author-stats {
    @apply justify-center;
  }
  
  .section-header {
    @apply flex-col items-start gap-3;
  }
  
  .section-title {
    @apply text-xl;
  }
  
  .content-section {
    @apply px-4 py-6;
  }
}

@media (max-width: 480px) {
  .stat-item {
    @apply px-3 py-1.5;
  }
  
  .stat-value {
    @apply text-lg;
  }
  
  .breadcrumb-section {
    @apply px-4;
  }
}

// ===== ANIMATIONS =====
.author-avatar {
  animation: fadeInScale 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.author-details {
  animation: fadeInUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s both;
}

.comics-grid {
  animation: fadeIn 0.4s ease-out 0.2s both;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .author-avatar,
  .author-details,
  .comics-grid {
    animation: none;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  .author-avatar {
    @apply ring-4 ring-black dark:ring-white;
  }
  
  .stat-item,
  .result-count {
    @apply border-2 border-black dark:border-white;
  }
  
  .retry-btn {
    @apply border-2 border-black dark:border-white;
  }
}
