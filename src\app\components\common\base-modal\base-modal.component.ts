import { Component, EventEmitter, Output, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-base-modal',
  imports: [CommonModule],
  templateUrl: './base-modal.component.html',
  styleUrl: './base-modal.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class BaseModalComponent {
  isVisible = false;

  @Output() submit = new EventEmitter<void>();

  show() {
    this.isVisible = true;
  }

  hide() {
    this.isVisible = false;
  }

  onSubmit() {
    this.submit.emit();
    this.hide();
  }
}
