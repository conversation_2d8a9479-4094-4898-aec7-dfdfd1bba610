import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Input, NgZone, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { timer } from 'rxjs';

@Component({
  selector: 'div[app-spinner]',
  templateUrl: './spinner.component.html',
  styleUrl: './spinner.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
})
export class SpinnerComponent {
  @Input() sizeSpinner = '12';
}
