'use client';

import { useEffect, useState } from 'react';
import styles from './page.module.css';

export default function Home() {
  const [isContentExpanded, setIsContentExpanded] = useState(false);
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(typeof window !== 'undefined');
  }, []);

  // Sample comic data
  const sampleComics = Array.from({ length: 12 }, (_, index) => ({
    id: index + 1,
    title: `Truyện tranh số ${index + 1}`,
    image: `/placeholder-comic-${(index % 3) + 1}.jpg`,
    latestChapter: `Chapter ${index + 10}`,
    updatedAt: '2 giờ trước',
    author: `Tác giả ${index + 1}`,
    genres: ['Action', 'Adventure'],
    rating: 4.5,
    views: 1000 + index * 100
  }));

  // Sample carousel data
  const carouselItems = [
    {
      id: 1,
      title: 'One Piece - Cuộc phiêu lưu vĩ đại',
      image: '/placeholder-carousel-1.jpg',
      description: '<PERSON> chân <PERSON> và băng hải tặc Mũ Rơm trong cuộc hành trình tìm kiếm kho báu One Piece',
      link: '/truyen/one-piece'
    },
    {
      id: 2,
      title: 'Attack on Titan - Cuộc chiến sinh tồn',
      image: '/placeholder-carousel-2.jpg',
      description: 'Nhân loại đối mặt với những Titan khổng lồ trong cuộc chiến sinh tồn tuyệt vọng',
      link: '/truyen/attack-on-titan'
    },
    {
      id: 3,
      title: 'Demon Slayer - Thanh gươm diệt quỷ',
      image: '/placeholder-carousel-3.jpg',
      description: 'Tanjiro bước vào hành trình trở thành thợ săn quỷ để cứu em gái',
      link: '/truyen/demon-slayer'
    }
  ];

  const toggleContent = () => {
    setIsContentExpanded(!isContentExpanded);
  };

  return (
    <div className={styles.homeContent}>  </div>
  );
}
