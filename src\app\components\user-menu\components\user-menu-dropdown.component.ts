import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { FadeInDirective } from '@directives/fade-in.directive';
import { IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { PopupService } from '@services/popup.service';
import { ToastService } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';

export interface UserMenuItem {
    id: string;
    label: string;
    type: 'link' | 'button';
    route?: string;
    action?: string;
    iconPath: string;
    secondaryIconPath?: string;
    disabled?: boolean;
}


@Component({
    selector: '[app-user-menu-dropdown]',
    templateUrl: './user-menu-dropdown.component.html',
    styleUrl: './user-menu-dropdown.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, FadeInDirective]
})
export class UserMenuDropdownComponent implements IPopupComponent {

    accountService = inject(AccountService);
    popupService = inject(PopupService);

    cd = inject(ChangeDetectorRef);

    user: IUser | undefined = undefined;
    isAuthenticated = false;
    notificationCount = 3;
    statusText = 'Đang hoạt động';

    isVisible = true;

    onVisibleChange = new EventEmitter<boolean>();

    menuItems: UserMenuItem[] = [
        {
            id: 'profile',
            label: 'Trang cá nhân',
            type: 'link',
            route: '/tai-khoan',
            iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
        },
        {
            id: 'following',
            label: 'Theo dõi',
            type: 'link',
            route: '/theo-doi',
            iconPath: 'M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z'
        },
        {
            id: 'settings',
            label: 'Cài đặt',
            type: 'button',
            action: 'settings',
            iconPath: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
            secondaryIconPath: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z'
        }
    ];

    constructor(private router: Router, private toastService: ToastService) { }
    show(object: any): Promise<any> {
        this.user = object.user;
        this.isAuthenticated = !!this.user;
        this.setVisible(true);

        return new Promise((resolve) => {
            const visibleSubscription = this.onVisibleChange.subscribe(() => {
                visibleSubscription.unsubscribe();
                resolve({ isVisible: false });
            });
        });
    }
    setVisible(isVisible: boolean): void {
        this.isVisible = isVisible;
        this.onVisibleChange.emit(isVisible);
        this.cd.detectChanges();
    }

    onMenuItemClick(item: UserMenuItem): void {
        if (item.disabled) {
            return;
        }
        if (item.type === 'link' && item.route) {
            this.router.navigate([item.route]);
            this.setVisible(false);
        } else if (item.type === 'button' && item.action) {
            this.handleButtonAction(item.action);
        }
    }

    handleButtonAction(action: string): void {
        switch (action) {
            case 'settings':
                this.popupService.showSetting();

                this.setVisible(false);
                break;
            default:
                console.warn(`Unknown action: ${action}`);
        }
    }

    onQuickAction(action: string): void {

        // Handle common quick actions
        switch (action) {
            case 'sync':
                // Navigate to notifications or open notifications panel
                this.router.navigate(['/dong-bo-truyen']);
                this.setVisible(false);
                break;
            case 'favorites':
                // this.router.navigate(['/yeu-thich']);
                // this.setVisible(false);
                this.toastService.info('Tính năng <b>Truyện yêu thích</b> đang được phát triển, vui lòng thử lại sau!');
                break;
            case 'history':
                this.router.navigate(['/lich-su']);
                this.setVisible(false);
                break;
        }
    }

    onLogout(): void {
        this.accountService.Logout();
        window.location.href = '/';
        this.setVisible(false);
    }

    onLogin(): void {
        this.router.navigate(['auth/login']);
        this.setVisible(false);
    }

    onRegister(): void {
        this.router.navigate(['auth/register']);
        this.setVisible(false);
    }

    trackByMenuItem(_: number, item: UserMenuItem): string {
        return item.id;
    }

    // Public methods for external control
    public openMenu(): void {
        // this.isVisible = true;
    }

    public getMenuState(): boolean {
        return this.isVisible;
    }


}
