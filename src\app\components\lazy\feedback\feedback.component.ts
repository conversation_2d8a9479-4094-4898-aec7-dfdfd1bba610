import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, Input, ViewEncapsulation } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AccountService } from '@services/account.service';
import { ReportErrorService } from '@services/reportError.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  selector: '[app-report-error]',
  templateUrl: './feedback.component.html',
  styleUrl: './feedback.component.scss',
  imports: [CommonModule, ReactiveFormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class FeedbackComponent implements IPopupComponent {
  private readonly destroyRef = inject(DestroyRef);

  @Input() isVisible = false;
  errorForm: FormGroup;
  chapterID = 0;

  constructor(
    private fb: FormBuilder,
    private toastService: ToastService,
    private reportErrorService: ReportErrorService,
    private accountService: AccountService,
    private cd: ChangeDetectorRef
  ) {
    this.errorForm = this.fb.group({
      message: ['', Validators.maxLength(1000)],
    });
  }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }
  show() {
    this.setVisible(true);
    return new Promise((resolve) => {
      resolve({});
    });
  }

  sendFeedback() {
    if (this.errorForm.valid) {
      const { message } = this.errorForm.value;
      const user = this.accountService.userSubject.value;
      const name = user?.email || '<EMAIL>';
      this.toastService.show(
        ToastType.Info,
        'Cảm ơn bạn đã báo góp ý, team sẽ xem xét và cải thiện nhé!',
      );

      this.reportErrorService
        .sendFeedback({
          mail: name,
          content: message,
        })
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: () => {
            this.toastService.show(ToastType.Success, 'Gửi thành công');
            this.setVisible(false)
            this.errorForm.reset();
          },
          error: (error) => {
            this.toastService.show(
              ToastType.Error,
              'Gửi thất bại, đã có lỗi xảy ra',
            );
            this.setVisible(false)
            console.error('Error sending report', error);
          },
        });
    } else {
      console.error('Form is invalid');
      this.setVisible(false)
      this.toastService.show(
        ToastType.Error,
        'Gửi thất thất bại, đã có lỗi xảy ra',
      );
      return;
    }
    this.setVisible(false)
  }

  
}
