// Optimized Carousel Component - Tailwind @apply with responsive utilities

// Desktop Carousel
.desktop-carousel {
  @apply w-full h-full absolute overflow-hidden pb-2 bg-dark-bg;
  @apply flex;
}

.nav-left {
  @apply top-1/2 left-2 -translate-y-1/2 w-12 h-12 absolute z-10 lg:h-full lg:w-1/4 cursor-pointer;
  @apply bg-black/40 hover:bg-black/60 rounded-xl lg:rounded-none;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .nav-icon {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 lg:w-20 lg:h-20 text-white opacity-10;
    @apply transition-opacity duration-200;
  }

  &:hover .nav-icon {
    @apply opacity-80;
  }
}
.nav-right {
  @apply top-1/2 right-2 -translate-y-1/2 w-12 h-12 absolute z-10 lg:h-full lg:w-1/4 cursor-pointer;
  @apply bg-black/40 hover:bg-black/60 rounded-xl lg:rounded-none;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .nav-icon {
    @apply absolute top-1/2 right-1/2 translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 lg:w-20 lg:h-20 text-white opacity-10;
    @apply transition-opacity duration-200;
  }

  &:hover .nav-icon {
    @apply opacity-80;
  }
}

// Comic Details Overlay
.details-overlay {
  @apply absolute left-1/4 w-2/4 h-full z-10;
  @apply bg-black/80 text-white overflow-hidden pointer-events-none;
  @apply transition-all duration-300;
}

.details-content {
  @apply flex flex-col p-3 gap-2;
}

.comic-header {
  @apply flex justify-between items-start gap-2;
}

.comic-title {
  @apply font-bold uppercase text-base leading-tight;
}

.status-container {
  @apply flex items-center gap-2 text-sm;
}

.status-ongoing {
  @apply w-1 h-1 rounded-full bg-sky-400 animate-ping opacity-75;
}

.status-completed {
  @apply w-1.5 h-1.5 animate-ping opacity-75;
}

// Genre Tags
.genre-tags {
  @apply flex flex-wrap gap-1;
}

.tag-primary {
  @apply bg-primary-100 text-white text-xs font-bold rounded px-2 py-0.5 uppercase cursor-pointer;
}

.tag-secondary {
  @apply bg-neutral-600 text-white text-xs font-semibold rounded px-2 py-0.5 uppercase cursor-pointer;
}

// Comic Stats
.comic-stats {
  @apply flex gap-3;
}
.item-overlay {
  @apply absolute bg-gradient-to-t from-black to-black/20 min-h-11 px-2 w-full bottom-0 flex items-center text-white text-lg font-semibold;
}
.stat-item {
  @apply flex items-center gap-1 text-sm;

  &:first-child {
    @apply text-yellow-500;
  }
}

.stat-icon {
  @apply w-4 h-4;
}

.comic-description {
  @apply mt-2;
}

.description-text {
  @apply text-sm leading-relaxed;
}

// Carousel Items
.carousel-item {
  @apply absolute top-0 h-full flex;
  @apply transition-[left] duration-700 ease-out;
}

.loading-container {
  @apply w-full h-full flex items-center justify-center;
}

// Swiper Component
.swiper-container {
  @apply absolute z-10 right-1/2 translate-x-1/2;
  @apply -bottom-4 md:-bottom-5;
}

.block-item {
  @apply flex mx-1 overflow-hidden shadow-md h-full w-full;
  
}
.item-image {
  @apply object-cover w-full h-full transition-[filter] hover:brightness-75;
}
.desktop-carousel {
  --carousel-grid: 7;
}
@media (max-width: 940px) {
  //
  .desktop-carousel {
    --carousel-grid: 4;
  }
}
@media (max-width: 640px) {
  .desktop-carousel {
    --carousel-grid: 3;
  }
}
@media (max-width: 480px) {
  .desktop-carousel {
    --carousel-grid: 2;
  }
}
