<!-- Enhanced User Header Component -->
<div class="user-header-container">
  <!-- Main Content -->
  <div *ngIf="!isLoading && user" class="header-content">
    <!-- Enhanced Profile Card -->
    <div class="profile-card">
      <div class="profile-card-header">
        <h2 class="card-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
            <circle cx="12" cy="7" r="4" />
          </svg>
          Thông tin cá nhân
        </h2>
      </div>

      <div class="profile-card-content">
        <!-- Avatar Section -->
        <div class="avatar-section">
          <div class="avatar-container">
            <label for="avatar-upload" class="avatar-upload-label">
              <img
                [src]="avatar"
                [alt]="user.firstName + ' ' + user.lastName"
                class="user-avatar"
                onerror="this.src='https://static.vecteezy.com/system/resources/previews/002/002/257/non_2x/beautiful-woman-avatar-character-icon-free-vector.jpg'"
              />
              <div class="avatar-overlay">
                <svg class="camera-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"
                  />
                  <circle cx="12" cy="13" r="4" />
                </svg>
                <span class="upload-text">Thay đổi</span>
              </div>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                (change)="onFileChange($event)"
                class="hidden"
              />
            </label>
          </div>

          <!-- Level Badge -->
          <div class="level-badge">
            <svg class="level-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="9" />
              <path d="M12 3a4.5 4.5 0 0 0 0 9a4.5 4.5 0 0 1 0 9" />
            </svg>
            <span class="level-text">{{ levelUser.level }}</span>
          </div>
        </div>

        <!-- User Stats -->
        <div class="user-stats">
          <div class="stat-item">
            <div class="stat-label">Kinh nghiệm</div>
            <div class="stat-value">{{ user.experience || 0 }} XP</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">Tiến độ</div>
            <div class="stat-value text-sky-600 dark:text-sky-400">{{ levelUser.percent }}%</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">Cấp độ tiếp theo</div>
            <div class="stat-value">{{ levelUser.nextLevel }}</div>
          </div>
        </div>

        <!-- Level Type Selection -->
        <div class="level-type-section">
          <h4 class="section-subtitle">Loại cấp độ</h4>
          <div class="level-type-options">
            <label class="level-type-option">
              <input
                type="radio"
                name="levelType"
                [value]="0"
                [checked]="user.typeLevel === 0"
                (change)="updateTypeLevel(0)"
                class="level-radio"
              />
              <div class="level-option-content">
                <span class="level-option-title">Tu tiên</span>
                <span class="level-option-desc">Hệ thống tu luyện</span>
              </div>
            </label>
            <label class="level-type-option">
              <input
                type="radio"
                name="levelType"
                [value]="1"
                [checked]="user.typeLevel === 1"
                (change)="updateTypeLevel(1)"
                class="level-radio"
              />
              <div class="level-option-content">
                <span class="level-option-title">Cấp độ</span>
                <span class="level-option-desc">Hệ thống level</span>
              </div>
            </label>
          </div>
        </div>

        <!-- Motto Section -->
        <div class="motto-section">
          <h4 class="section-subtitle">Châm ngôn</h4>
          <div class="motto-input-group">
            <textarea
              [(ngModel)]="maxim"
              placeholder="Nhập châm ngôn của bạn..."
              class="motto-textarea"
              rows="3"
            ></textarea>
            <button
              (click)="onUpdateMaxim(maxim)"
              class="motto-save-btn"
              [disabled]="maxim === user.maxim"
            >
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                <polyline points="17,21 17,13 7,13 7,21" />
                <polyline points="7,3 7,8 15,8" />
              </svg>
              Lưu
            </button>
          </div>
        </div>

        <!-- Join Date -->
        <div class="join-date-section">
          <div class="join-date-item">
            <svg class="calendar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <div class="join-date-content">
              <span class="join-date-label">Ngày tham gia</span>
              <span class="join-date-value">{{ user.createAt | date : 'dd/MM/yyyy' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Information Form Card -->
    <div class="info-card">
      <div class="info-card-header">
        <h3 class="card-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
            <polyline points="14,2 14,8 20,8" />
            <line x1="16" y1="13" x2="8" y2="13" />
            <line x1="16" y1="17" x2="8" y2="17" />
            <polyline points="10,9 9,9 8,9" />
          </svg>
          Thông tin chi tiết
        </h3>
        <button (click)="toggleEditProfile()" class="edit-btn" [class.active]="isEditingProfile">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
          </svg>
          {{ isEditingProfile ? 'Hủy' : 'Chỉnh sửa' }}
        </button>
      </div>

      <div class="info-card-content">
        <form
          [formGroup]="infoForm"
          (ngSubmit)="onUpdateInfo()"
          *ngIf="isEditingProfile; else viewMode"
        >
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">
                <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="9" />
                  <line x1="9" y1="10" x2="9.01" y2="10" />
                  <line x1="15" y1="10" x2="15.01" y2="10" />
                  <path d="M9.5 15a3.5 3.5 0 0 0 5 0" />
                </svg>
                Tên
              </label>
              <input
                type="text"
                formControlName="firstName"
                class="form-input"
                [class.error]="isControlInvalid('firstName', infoForm)"
                placeholder="Nhập tên của bạn"
              />
              <div *ngIf="isControlInvalid('firstName', infoForm)" class="error-message">
                Vui lòng nhập tên
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="7" r="4" />
                  <path d="M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" />
                </svg>
                Họ
              </label>
              <input
                type="text"
                formControlName="lastName"
                class="form-input"
                [class.error]="isControlInvalid('lastName', infoForm)"
                placeholder="Nhập họ của bạn"
              />
              <div *ngIf="isControlInvalid('lastName', infoForm)" class="error-message">
                Vui lòng nhập họ
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  />
                  <polyline points="22,6 12,13 2,6" />
                </svg>
                Email
              </label>
              <input
                type="email"
                formControlName="email"
                class="form-input"
                [class.error]="isControlInvalid('email', infoForm)"
                placeholder="<EMAIL>"
              />
              <div *ngIf="isControlInvalid('email', infoForm)" class="error-message">
                <span *ngIf="infoForm.get('email')?.hasError('required')">Vui lòng nhập email</span>
                <span *ngIf="infoForm.get('email')?.hasError('email')">Email không hợp lệ</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">
                <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                  <line x1="16" y1="2" x2="16" y2="6" />
                  <line x1="8" y1="2" x2="8" y2="6" />
                  <line x1="3" y1="10" x2="21" y2="10" />
                </svg>
                Ngày sinh
              </label>
              <input
                type="date"
                formControlName="dob"
                class="form-input"
                [class.error]="isControlInvalid('dob', infoForm)"
              />
              <div *ngIf="isControlInvalid('dob', infoForm)" class="error-message">
                Vui lòng chọn ngày sinh
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" (click)="toggleEditProfile()" class="cancel-btn">Hủy</button>
            <button type="submit" class="save-btn" [disabled]="!infoForm.valid || !infoForm.dirty">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="20,6 9,17 4,12" />
              </svg>
              Lưu thay đổi
            </button>
          </div>
        </form>

        <ng-template #viewMode>
          <div class="info-display">
            <div class="info-item">
              <span class="info-label">Họ và tên</span>
              <span class="info-value">{{ user.firstName }} {{ user.lastName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Email</span>
              <span class="info-value">{{ user.email }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Ngày sinh</span>
              <span class="info-value">{{ user.dob | date : 'dd/MM/yyyy' }}</span>
            </div>
          </div>
        </ng-template>
      </div>
    </div>

    <!-- Enhanced Password Form Card -->
    <div class="password-card">
      <div class="password-card-header">
        <h3 class="card-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
            <circle cx="12" cy="16" r="1" />
            <path d="M7 11V7a5 5 0 0 1 10 0v4" />
          </svg>
          Bảo mật
        </h3>
        <button (click)="toggleEditPassword()" class="edit-btn" [class.active]="isEditingPassword">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
          </svg>
          {{ isEditingPassword ? 'Hủy' : 'Đổi mật khẩu' }}
        </button>
      </div>

      <div class="password-card-content" *ngIf="isEditingPassword">
        <form [formGroup]="passwordForm" (ngSubmit)="onUpdatePassword()">
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">Mật khẩu hiện tại</label>
              <div class="password-input-group">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  formControlName="oldPassword"
                  class="form-input"
                  [class.error]="isControlInvalid('oldPassword', passwordForm)"
                  placeholder="Nhập mật khẩu hiện tại"
                />
                <div
                  app-eye-icon
                  [show]="showPassword"
                  (click)="showPassword = !showPassword"
                  class="password-toggle"
                ></div>
              </div>
              <div *ngIf="isControlInvalid('oldPassword', passwordForm)" class="error-message">
                Vui lòng nhập mật khẩu hiện tại
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">Mật khẩu mới</label>
              <div class="password-input-group">
                <input
                  [type]="showNewPassword ? 'text' : 'password'"
                  formControlName="newPassword"
                  class="form-input"
                  [class.error]="isControlInvalid('newPassword', passwordForm)"
                  placeholder="Nhập mật khẩu mới"
                />
                <div
                  app-eye-icon
                  [show]="showNewPassword"
                  (click)="showNewPassword = !showNewPassword"
                  class="password-toggle"
                ></div>
              </div>
              <div *ngIf="isControlInvalid('newPassword', passwordForm)" class="error-message">
                Vui lòng nhập mật khẩu mới
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">Xác nhận mật khẩu</label>
              <div class="password-input-group">
                <input
                  [type]="showRePassword ? 'text' : 'password'"
                  formControlName="rePassword"
                  class="form-input"
                  [class.error]="isControlInvalid('rePassword', passwordForm)"
                  placeholder="Nhập lại mật khẩu mới"
                />
                <div
                  app-eye-icon
                  [show]="showRePassword"
                  (click)="showRePassword = !showRePassword"
                  class="password-toggle"
                ></div>
              </div>
              <div *ngIf="isControlInvalid('rePassword', passwordForm)" class="error-message">
                Vui lòng xác nhận mật khẩu
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" (click)="toggleEditPassword()" class="cancel-btn">Hủy</button>
            <button type="submit" class="save-btn" [disabled]="!passwordForm.valid">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="20,6 9,17 4,12" />
              </svg>
              Cập nhật mật khẩu
            </button>
          </div>
        </form>
      </div>

      <div class="password-card-content" *ngIf="!isEditingPassword">
        <div class="password-info">
          <div class="password-status">
            <svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 12l2 2 4-4" />
              <circle cx="12" cy="12" r="9" />
            </svg>
            <span class="status-text">Mật khẩu được bảo vệ</span>
          </div>
          <p class="password-description">
            Mật khẩu của bạn được mã hóa và bảo mật. Nhấn "Đổi mật khẩu" để cập nhật.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
