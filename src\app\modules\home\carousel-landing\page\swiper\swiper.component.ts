import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Output,
    ViewEncapsulation,
    signal
} from '@angular/core';

@Component({
  selector: '[app-swiper]',
  templateUrl: './swiper.component.html',
  styleUrl: './swiper.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
})
export class SwiperComponent {
  // Static configuration
  readonly Btns: number[] = [1, 2, 3, 4, 5, 6, 7, 8];
  readonly width = 20;

  // State using signals for better reactivity
  value = signal<number>(0);
  current = signal<number>(4);
  lastTime = signal<number>(0);

  @Output() nextChange = new EventEmitter<number>();

  constructor(private cd: ChangeDetectorRef) {
  }



  getLeftPositon(index: number): string {
    let v = this.value() + (index) * this.width;
    v %= this.width * this.Btns.length
    v -= this.width * 2
    return `${v}px`;
  }
  
  click(id: number) {
    const now = Date.now();
    if (now - this.lastTime() < 500) {
      return;
    }
    this.lastTime.set(now);
    let direct = ((id - this.current()) % this.Btns.length + this.Btns.length) % this.Btns.length;
    this.change(direct);
    if (direct >= this.Btns.length - 2) {
      direct = direct - this.Btns.length
    }
    this.nextChange.emit(direct);
  }
  
  next() {
    this.change(1);
  }
  
  prev() {
    this.change(this.Btns.length - 1);
  }
  
  change(direct: number) {
    this.value.update(current => current + this.width * direct);
    this.current.update(current => 
      ((current - direct) % this.Btns.length + this.Btns.length) % this.Btns.length
    );
    this.cd.detectChanges();
  }
}
