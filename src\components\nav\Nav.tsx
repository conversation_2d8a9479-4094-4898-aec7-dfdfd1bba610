"use client"
import Link from "next/link";
import { useEffect, useState } from "react";

export default function Nav() {
  // State
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [isShowGenre, setIsShowGenre] = useState(false);
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(typeof window !== "undefined");
    const theme = typeof window !== "undefined" ? localStorage.getItem("theme") : "light";
    setIsDarkMode(theme === "dark");
  }, []);

  const toggleTheme = () => {
    const newTheme = isDarkMode ? "light" : "dark";
    setIsDarkMode(!isDarkMode);
    if (typeof window !== "undefined") {
      localStorage.setItem("theme", newTheme);
      document.documentElement.classList.toggle("dark", newTheme === "dark");
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      if (showSidebar) setShowSidebar(false);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [showSidebar]);

  const menu = [
    { href: "/truyen-hot", label: "Hot" },
    { href: "/theo-doi", label: "Theo dõi" },
    { href: "/xep-hang", label: "Xếp hạng" },
    { href: "/tim-truyen", label: "Tìm kiếm nâng cao" },
    { href: "/lich-su", label: "Lịch sử" },
    { href: "/tro-ly-ai", label: "Trợ lý AI" },
  ];

  return (
    <>
      <header className="nav-header">
        <div className="nav-container">
          <Link href="/" className="nav-logo">
            <img src="/logo.png" alt="logo" className="w-[128px] h-[60px]" loading="eager" />
          </Link>
          {isBrowser && (
            <div className="nav-actions">
              <div className="theme-toggle">
                <input
                  aria-label="Toggle theme"
                  id="theme-toggle"
                  type="checkbox"
                  checked={isDarkMode}
                  onChange={toggleTheme}
                  className="theme-input"
                />
                <span className={`theme-slider${isDarkMode ? " active" : ""}`}>
                  {isDarkMode ? (
                    <svg className="theme-icon moon" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M14.353 10.62c-.107-.18-.407-.46-1.153-.327-.414.073-.834.107-1.254.087-1.553-.067-2.96-.78-3.94-1.88-.866-.967-1.4-2.227-1.406-3.587 0-.76.146-1.493.446-2.187.294-.673.087-1.027-.06-1.173-.153-.154-.513-.367-1.22-.067C3.04 2.627 1.353 5.36 1.553 8.287c.2 2.76 2.133 6.113 4.693 7 .614.213 1.26.34 1.927.367.067.006.174.013.28.013 2.234 0 4.327-1.053 5.647-2.847.447-.62.327-1.013.213-1.193z" />
                    </svg>
                  ) : (
                    <svg className="theme-icon sun" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M8 12.667A4.667 4.667 0 1 0 8 3.333a4.667 4.667 0 0 0 0 9.334z" />
                      <path d="M8 15.307a.667.667 0 0 1-.667-.667v-.053a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.72zm4.76-1.88a.667.667 0 0 1-.473-.194l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.134zm-9.52 0a.667.667 0 0 1-.473-1.134l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.194zM14.667 8.667h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm-13.28 0h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm11.286-4.674a.667.667 0 0 1-.473-.193.667.667 0 0 1 0-.94l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.193zm-9.346 0a.667.667 0 0 1-.473-.193l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.133zM8 2.027a.667.667 0 0 1-.667-.667V1.333a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.694z" />
                    </svg>
                  )}
                </span>
              </div>
            </div>
          )}
        </div>
      </header>
      <nav className="nav-main">
        <div className="nav-mobile">
          <div className="nav-home">
            <svg className="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
              <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <Link href="/">Trang chủ</Link>
          </div>
          <button
            type="button"
            aria-label="Toggle menu"
            className="nav-toggle"
            onClick={() => setShowSidebar((v) => !v)}
          >
            {showSidebar ? (
              <svg className="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            )}
          </button>
        </div>
        <ul className="nav-desktop">
          <li>
            <Link href="/" aria-label="Trang chủ" className="nav-link">
              <svg className="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </Link>
          </li>
          {menu.map((item) => (
            <li key={item.href}>
              <Link href={item.href} className="nav-link">
                <span>{item.label}</span>
              </Link>
            </li>
          ))}
          <li className="nav-dropdown" onMouseEnter={() => setIsShowGenre(true)} onMouseLeave={() => setIsShowGenre(false)}>
            <div className="nav-link">
              <svg className="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              Thể loại
            </div>
            {isShowGenre && isBrowser && (
              <div className="nav-dropdown-menu">
                <div className="loading">Đang tải...</div>
              </div>
            )}
          </li>
          <li>
            <span className="nav-link cursor-pointer" onClick={() => alert("Góp ý")}>Góp ý</span>
          </li>
        </ul>
        <ul className={`nav-mobile-menu ${showSidebar ? "open" : "closed"}`}>
          {menu.map((item) => (
            <li key={item.href}>
              <Link href={item.href} className="nav-mobile-link" onClick={() => setShowSidebar(false)}>
                <span>{item.label}</span>
              </Link>
            </li>
          ))}
          <li className="nav-mobile-dropdown" onMouseEnter={() => setIsShowGenre(true)} onMouseLeave={() => setIsShowGenre(false)}>
            <div className="nav-mobile-link">Thể loại</div>
            {isShowGenre && isBrowser && (
              <div className="nav-mobile-dropdown-menu">
                <div className="loading">Đang tải...</div>
              </div>
            )}
          </li>
          <li>
            <span className="nav-mobile-link cursor-pointer" onClick={() => { alert("Góp ý"); setShowSidebar(false); }}>Góp ý</span>
          </li>
          <li>
            <span className="nav-mobile-link cursor-pointer" onClick={() => { alert("Cài đặt"); setShowSidebar(false); }}>Cài đặt</span>
          </li>
        </ul>
      </nav>
    </>
  );
}
