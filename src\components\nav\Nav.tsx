'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import styles from './Nav.module.css';

interface NavProps {
  isDarkMode: boolean;
  toggleTheme: () => void;
  isBrowser: boolean;
}

export default function Nav({ isDarkMode, toggleTheme, isBrowser }: NavProps) {
  const [showSidebar, setShowSidebar] = useState(false);
  const [isShowGenre, setIsShowGenre] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (showSidebar) setShowSidebar(false);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [showSidebar]);

  const menu = [
    { href: "/truyen-hot", label: "Hot" },
    { href: "/theo-doi", label: "<PERSON> dõi" },
    { href: "/xep-hang", label: "<PERSON>ếp hạng" },
    { href: "/tim-truyen", label: "Tìm kiếm nâng cao" },
    { href: "/lich-su", label: "L<PERSON>ch sử" },
    { href: "/tro-ly-ai", label: "Trợ lý AI" },
  ];

  return (
    <>
      <header className={styles.navHeader}>
        <div className={styles.navContainer}>
          <Link href="/" className={styles.navLogo}>
            <img src="/logo.png" alt="logo" className="w-[128px] h-[60px]" loading="eager" />
          </Link>
          {isBrowser && (
            <div className={styles.navActions}>
              <div className={styles.themeToggle}>
                <input
                  aria-label="Toggle theme"
                  id="theme-toggle"
                  type="checkbox"
                  checked={isDarkMode}
                  onChange={toggleTheme}
                  className={styles.themeInput}
                />
                <span className={`${styles.themeSlider}${isDarkMode ? ` ${styles.active}` : ""}`}>
                  {isDarkMode ? (
                    <svg className={`${styles.themeIcon} ${styles.moon}`} viewBox="0 0 16 16" fill="currentColor">
                      <path d="M14.353 10.62c-.107-.18-.407-.46-1.153-.327-.414.073-.834.107-1.254.087-1.553-.067-2.96-.78-3.94-1.88-.866-.967-1.4-2.227-1.406-3.587 0-.76.146-1.493.446-2.187.294-.673.087-1.027-.06-1.173-.153-.154-.513-.367-1.22-.067C3.04 2.627 1.353 5.36 1.553 8.287c.2 2.76 2.133 6.113 4.693 7 .614.213 1.26.34 1.927.367.067.006.174.013.28.013 2.234 0 4.327-1.053 5.647-2.847.447-.62.327-1.013.213-1.193z" />
                    </svg>
                  ) : (
                    <svg className={`${styles.themeIcon} ${styles.sun}`} viewBox="0 0 16 16" fill="currentColor">
                      <path d="M8 12.667A4.667 4.667 0 1 0 8 3.333a4.667 4.667 0 0 0 0 9.334z" />
                      <path d="M8 15.307a.667.667 0 0 1-.667-.667v-.053a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.72zm4.76-1.88a.667.667 0 0 1-.473-.194l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.134zm-9.52 0a.667.667 0 0 1-.473-1.134l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.194zM14.667 8.667h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm-13.28 0h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm11.286-4.674a.667.667 0 0 1-.473-.193.667.667 0 0 1 0-.94l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.193zm-9.346 0a.667.667 0 0 1-.473-.193l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.133zM8 2.027a.667.667 0 0 1-.667-.667V1.333a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.694z" />
                    </svg>
                  )}
                </span>
              </div>
            </div>
          )}
        </div>
      </header>
      <nav className={styles.navMain}>
        <div className={styles.navMobile}>
          <div className={styles.navHome}>
            <svg className={styles.navIcon} viewBox="0 0 24 24" stroke="currentColor" fill="none">
              <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <Link href="/">Trang chủ</Link>
          </div>
          <button
            type="button"
            aria-label="Toggle menu"
            className={styles.navToggle}
            onClick={() => setShowSidebar((v) => !v)}
          >
            {showSidebar ? (
              <svg className={styles.navIcon} viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className={styles.navIcon} viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            )}
          </button>
        </div>
        <ul className={styles.navDesktop}>
          <li>
            <Link href="/" aria-label="Trang chủ" className={styles.navLink}>
              <svg className={styles.navIcon} viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </Link>
          </li>
          {menu.map((item) => (
            <li key={item.href}>
              <Link href={item.href} className={styles.navLink}>
                <span>{item.label}</span>
              </Link>
            </li>
          ))}
          <li className={styles.navDropdown} onMouseEnter={() => setIsShowGenre(true)} onMouseLeave={() => setIsShowGenre(false)}>
            <div className={styles.navLink}>
              <svg className={styles.navIcon} viewBox="0 0 24 24" stroke="currentColor" fill="none">
                <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              Thể loại
            </div>
            {isShowGenre && isBrowser && (
              <div className={styles.navDropdownMenu}>
                <div className={styles.loading}>Đang tải...</div>
              </div>
            )}
          </li>
          <li>
            <span className={`${styles.navLink} cursor-pointer`} onClick={() => alert("Góp ý")}>Góp ý</span>
          </li>
        </ul>
        <ul className={`${styles.navMobileMenu} ${showSidebar ? styles.open : styles.closed}`}>
          {menu.map((item) => (
            <li key={item.href}>
              <Link href={item.href} className={styles.navMobileLink} onClick={() => setShowSidebar(false)}>
                <span>{item.label}</span>
              </Link>
            </li>
          ))}
          <li className={styles.navMobileDropdown} onMouseEnter={() => setIsShowGenre(true)} onMouseLeave={() => setIsShowGenre(false)}>
            <div className={styles.navMobileLink}>Thể loại</div>
            {isShowGenre && isBrowser && (
              <div className={styles.navMobileDropdownMenu}>
                <div className={styles.loading}>Đang tải...</div>
              </div>
            )}
          </li>
          <li>
            <span className={`${styles.navMobileLink} cursor-pointer`} onClick={() => { alert("Góp ý"); setShowSidebar(false); }}>Góp ý</span>
          </li>
          <li>
            <span className={`${styles.navMobileLink} cursor-pointer`} onClick={() => { alert("Cài đặt"); setShowSidebar(false); }}>Cài đặt</span>
          </li>
        </ul>
      </nav>
    </>
  );
}
