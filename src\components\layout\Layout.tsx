'use client';

import { useEffect, useState } from 'react';
import Nav from '../nav/Nav';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const [currentTheme, setCurrentTheme] = useState(false); // false = light, true = dark
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(true);
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      setCurrentTheme(savedTheme === 'dark');
    } else {
      // Check system preference
      setCurrentTheme(window.matchMedia('(prefers-color-scheme: dark)').matches);
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = !currentTheme;
    setCurrentTheme(newTheme);
    localStorage.setItem('theme', newTheme ? 'dark' : 'light');
  };

  return (
    <div className={`${currentTheme ? 'dark' : 'light'} bg-white dark:bg-dark-bg dark:text-white`}>
      <Nav isDarkMode={currentTheme} toggleTheme={toggleTheme} isBrowser={isBrowser} />
      <main className="main">
        {children}
      </main>
      {/* <Footer /> */}
      {isBrowser && (
        <div className="chat-box-bubble"></div>
      )}
      <div id="popupContainer"></div>
    </div>
  );
}
