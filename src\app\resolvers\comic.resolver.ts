import { inject, RESPONSE_INIT } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { UrlService } from '@services/url.service';
import { catchError, EMPTY, map, of } from 'rxjs';




export const ComicResolver: ResolveFn<any> = (route) => {
  const domainType = inject(UrlService).domainType;
  let slug: any = route.paramMap.get('slug');
  let id: any = slug;

  const number = Number(slug?.split('-').pop() || '');
  if (domainType === 1 && Number.isInteger(number) && number > 10) {
    slug = slug?.replace(`-${number}`, '');
    let responseInit = inject(RESPONSE_INIT);
    
    if (responseInit) {
      responseInit.status = 301;
      responseInit.headers = {
        location: '/truyen-tranh/' + slug
      };
      return EMPTY;

    }
    return { response: null, redirect: ['/truyen-tranh', slug] };
  }

  if (domainType === 0 && Number.isInteger(number)) {
    id = number;
  }
  return inject(ComicService).getComicById(id)
    .pipe(
      map((res) => {
        return { response: res, redirect: false };
      }),
      catchError((err) => {
        return of({ response: null, redirect: ['/'] });
      })
    );
};
export const ChapterImgsResolver: ResolveFn<any> = (route) => {
  const comicKey = route.paramMap.get('comickey');
  const chapterkey = route.paramMap.get('chapterkey')
  if (!comicKey || !chapterkey) {
    return of(null);
  }

  return inject(ComicService).getChapterImgs(comicKey, chapterkey)
    .pipe(
      map((res) => {
        return { response: res, redirect: false, chapterkey: chapterkey };
      }),
      catchError((err) => {
        return of({ response: null, redirect: ['/'] });
      })
    );
};

export const FollowedComicResolver: ResolveFn<any> = (route, state) => {
  return inject(AccountService).GetFollowedComics();
};


export const RecommendComicResolver: ResolveFn<any> = (route, state) => {
  return inject(ComicService).getRecommendComics();
};



export const HotComicResolver: ResolveFn<any> = (route, state) => {
  const page = Number(route.paramMap.get('page')) || 1;
  return inject(ComicService).getHotComics(page);
};


