<!-- Modern Comic-themed Login Container -->
<div class="auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="comic-bubbles">
      <div class="bubble bubble-1">📚</div>
      <div class="bubble bubble-2">🎨</div>
      <div class="bubble bubble-3">⭐</div>
      <div class="bubble bubble-4">💫</div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="auth-content">
    <!-- Left Side - Branding -->
    <div class="auth-branding">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">
            <img loading="lazy" src="/favicon.png" alt="MeTruyenMoi Logo" />
          </div>
          <h1 class="brand-title">MeTruyen<span class="brand-accent">Moi</span></h1>
        </div>
        <p class="brand-subtitle"><PERSON><PERSON><PERSON> cập nhật truyện mới mỗi ngày</p>
        <div class="brand-features">
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Hàng nghìn bộ truyện hot</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Cập nhật liên tục</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Gợi ý cá nhân hóa</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Login Form -->
    <div class="auth-form-container">
      <div class="auth-form-card">
        <!-- Header -->
        <div class="form-header">
          <h2 class="form-title">Chào mừng trở lại!</h2>
          <p class="form-subtitle">Đăng nhập để tiếp tục hành trình đọc truyện của bạn</p>
        </div>

        <!-- Login Form -->
        <form class="auth-form" (submit)="onSubmit()" [formGroup]="form">
          <!-- Email Field -->
          <div class="form-group">
            <label for="email" class="form-label">
              <span class="label-icon"><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </span>
              Email
            </label>
            <div class="input-wrapper">
              <input id="email" name="email" type="email" formControlName="email"
                placeholder="Nhập địa chỉ email của bạn" class="form-input" autocomplete="email" required />
              <div class="input-icon">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('email')">
              <span *ngIf="form.get('email')?.hasError('required')">
                Vui lòng nhập địa chỉ email
              </span>
              <span *ngIf="form.get('email')?.hasError('email') && submitted">
                Định dạng email không hợp lệ
              </span>
            </div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <label for="password" class="form-label">
              <span class="label-icon"><svg class="h-6 w-6" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                  stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <rect x="5" y="11" width="14" height="10" rx="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                </svg></span>
              Mật khẩu
            </label>
            <div class="input-wrapper">
              <input id="password" name="password" [type]="showPassword ? 'text' : 'password'"
                formControlName="password" placeholder="Nhập mật khẩu của bạn" class="form-input"
                autocomplete="current-password" required />
              <button type="button" (click)="showPassword = !showPassword" class="password-toggle">
                <div app-eye-icon [show]="showPassword"></div>
              </button>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('password')">
              <span *ngIf="form.get('password')?.hasError('required')">
                Vui lòng nhập mật khẩu
              </span>
            </div>
          </div>

          <!-- Remember & Forgot Password -->
          <div class="form-options">
            <div class="remember-me">
              <input id="remember_me" name="remember_me" formControlName="remember" type="checkbox"
                class="form-checkbox" />
              <label for="remember_me" class="checkbox-label"> Ghi nhớ đăng nhập </label>
            </div>

            <a [routerLink]="['/auth/forgot-password']" class="forgot-password-link">
              Quên mật khẩu?
            </a>
          </div>

          <!-- Submit Button -->
          <button type="submit" class="submit-button" [disabled]="form.invalid">
            <span class="button-content">
              <svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              <span class="button-text">Đăng nhập</span>
            </span>
          </button>
        </form>

        <!-- Divider -->
        <div class="form-divider">
          <div class="divider-line"></div>
          <span class="divider-text">Hoặc đăng nhập với</span>
          <div class="divider-line"></div>
        </div>

        <!-- Social Login -->
        <div class="social-login">
          <asl-google-signin-button type="standard" size="large" shape="rectangular" theme="filled_black" [width]="280"
            class="google-button">
          </asl-google-signin-button>
        </div>

        <!-- Register Link -->
        <div class="auth-switch">
          <p class="switch-text">
            Chưa có tài khoản?
            <a [routerLink]="['/auth/register']" class="switch-link"> Đăng ký ngay </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>