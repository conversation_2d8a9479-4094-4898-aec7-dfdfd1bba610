/* Grid Comic Component Styles */

/* Container */
.gridContainer {
  margin-top: 0;
  margin-bottom: 0.75rem;
}

/* Header */
.gridHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.titleSection {
  display: flex;
  align-items: center;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
  border-radius: 0.125rem;
}

.titleSection {
  color: rgb(55 65 81);
}

:global(.dark) .titleSection {
  color: white;
}

.blockTitle {
  font-size: 1.25rem;
  line-height: 1.75rem;
  text-transform: uppercase;
  font-weight: 800;
  line-height: 0.75rem;
  display: flex;
  min-height: 100%;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
  align-items: center;
  justify-content: center;
  color: rgb(55 65 81);
  margin-top: 0.125rem;
}

:global(.dark) .blockTitle {
  color: white;
}

/* Grid Type Switch */
.gridSwitch {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgb(245 245 245);
  border-radius: 0.375rem;
  padding: 0.125rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border: 1px solid rgb(229 229 229);
}

:global(.dark) .gridSwitch {
  background-color: rgb(64 64 64);
  border-color: rgb(82 82 82);
}

.switchTrack {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  bottom: 0.25rem;
  left: 0.25rem;
  pointer-events: none;
}

.switchThumb {
  width: 50%;
  height: 100%;
  background-color: rgb(64 64 64);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

:global(.dark) .switchThumb {
  background-color: rgb(23 23 23);
}

.switchThumb.listMode {
  transform: translateX(0);
}

.switchThumb.gridMode {
  transform: translateX(100%);
}

.switchBtn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  min-width: 2.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: rgb(82 82 82);
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  z-index: 10;
  transition: color 200ms;
}

:global(.dark) .switchBtn {
  color: rgb(163 163 163);
}

.switchBtn:hover {
  color: rgb(23 23 23);
}

:global(.dark) .switchBtn:hover {
  color: white;
}

.switchBtn.active {
  color: white;
}

.switchIcon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Grid Content */
.gridItem {
  position: relative;
}

.listGrid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1280px) {
  .listGrid {
    gap: 1rem;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1536px) {
  .listGrid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.listItemV2 {
  position: relative;
}

@media (min-width: 1024px) {
  .listItemV2 {
    padding-left: 0;
    padding-right: 0;
  }
}

/* Comic Popup */
.comicPopup {
  position: fixed;
  z-index: 20;
  margin-top: 0.5rem;
  transition: all 100ms;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gridHeader {
    flex-direction: column;
    gap: 0.75rem;
  }

  .titleSection {
    justify-content: center;
  }

  .blockTitle {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .gridSwitch {
    width: 100%;
    justify-content: center;
  }
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Line Clamp Utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
