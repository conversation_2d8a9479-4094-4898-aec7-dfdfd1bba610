@import 'tailwindcss';
/* Navigation Header */
.navHeader {
  @apply sticky top-0 z-50 bg-white border-b border-gray-200 border-gray-700;
}

.navContainer {
  @apply container mx-auto px-4 py-3 flex items-center justify-between;
}

.navLogo {
  @apply flex items-center space-x-2;
}

.navActions {
  @apply flex items-center space-x-4;
}

/* Theme Toggle */
.themeToggle {
  @apply relative;
}

.themeInput {
  @apply sr-only;
}

.themeSlider {
  @apply relative flex items-center justify-center w-12 h-6 bg-gray-300 rounded-full cursor-pointer transition-colors duration-300;
  @apply bg-gray-600;
}

.themeSlider.active {
  @apply bg-blue-500 bg-blue-600;
}

.themeIcon {
  @apply w-4 h-4 transition-opacity duration-300;
}

.sun {
  @apply text-yellow-500 opacity-100;
}

.moon {
  @apply text-blue-200 opacity-0 absolute;
}

.themeSlider.active .sun {
  @apply opacity-0;
}

.themeSlider.active .moon {
  @apply opacity-100;
}

/* Navigation Main */
.navMain {
  @apply bg-gray-50 bg-gray-800 border-b border-gray-200 border-gray-700;
}

.navMobile {
  @apply container mx-auto px-4 py-2 flex items-center justify-between hidden;
}

.navHome {
  @apply flex items-center space-x-2 text-gray-700 text-gray-300;
}

.navIcon {
  @apply w-5 h-5;
}

.navToggle {
  @apply p-2 rounded-md text-gray-700 text-gray-300 bg-gray-200 bg-gray-700;
}

/* Navigation Menu */
.navMenu {
  @apply hidden block;
}

.navMenuOpen {
  @apply block hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 border-gray-700 z-40;
}

.navLinks {
  @apply container mx-auto px-4 py-4 space-y-2;
}

.navLink {
  @apply flex items-center space-x-2 px-3 py-2 rounded-md text-gray-700 text-gray-300 bg-gray-100 bg-gray-700 transition-colors duration-200;
}

/* Desktop Navigation */
.navDesktop {
  @apply hidden flex items-center justify-center space-x-6 container mx-auto px-4 py-2;
}

.navDesktop .navLink {
  @apply text-gray-700 text-gray-300 text-blue-600 text-blue-400;
}

/* Mobile Menu */
.navMobileMenu {
  @apply hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 border-gray-700 z-40 transform transition-transform duration-300;
}

.navMobileMenu.open {
  @apply translate-y-0;
}

.navMobileMenu.closed {
  @apply -translate-y-full;
}

.navMobileLink {
  @apply flex items-center space-x-2 px-4 py-3 text-gray-700 text-gray-300 bg-gray-100 bg-gray-700 border-b border-gray-100 border-gray-700;
}

/* Dropdown */
.navDropdown {
  @apply relative;
}

.navDropdownMenu {
  @apply absolute top-full left-0 mt-1 bg-white border border-gray-200 border-gray-700 rounded-md shadow-lg z-50 min-w-48;
}

.navMobileDropdown {
  @apply relative;
}

.navMobileDropdownMenu {
  @apply bg-gray-50 bg-gray-700 border-t border-gray-200 border-gray-600;
}

/* Loading */
.loading {
  @apply p-4 text-center text-gray-500 text-gray-400;
}
