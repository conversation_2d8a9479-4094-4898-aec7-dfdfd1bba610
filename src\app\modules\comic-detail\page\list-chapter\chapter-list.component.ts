import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  PLATFORM_ID,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoopScrollComponent } from '@components/common/loop-scroll/loop-scroll.component';
import { SelectionComponent } from '@components/common/selection/selection.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Chapter, Comic } from '@schema';
import { HistoryService } from '@services/history.service';
import { SettingService } from '@services/setting.service';
import { UrlService } from '@services/url.service';
import config from 'globalConfig';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: '[app-chapter-list]',
  templateUrl: './chapter-list.component.html',
  styleUrl: './chapter-list.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, RouterModule, SelectionComponent, DateAgoPipe, LoopScrollComponent, SpinnerComponent],

})
export class ChapterListComponent implements OnInit, AfterViewInit, OnChanges {
  
  onChangeSelection($event: any) {
    this.loopScrollComponent.goToItem($event * this.distance)
  }


  @Input()
  comic?: Comic;
  @Input()
  allchapters: Chapter[] = [];
  @Input()
  isChapterLoading = false;
  preLoadChapters: Chapter[] = [];
  history: number[] = [];
  options: IOption[] = [];
  asc = false;
  // Constants
  gridSize = 3;
  curOptioneValue = 0;
  distance = 100;
  // gridType = 'nhieu';
  isBrowser = isPlatformBrowser(this.platformId);
  @ViewChild('LoopScroll')
  loopScrollComponent!: LoopScrollComponent;

  constructor(
    private cd: ChangeDetectorRef,
    private historyService: HistoryService,
    private settingService: SettingService,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: object,
  ) { }
  ngOnInit() {

    if (isPlatformBrowser(this.platformId)) {
      // this.gridType = this.settingService.getSettingValue('cardChapterSize') ?? 'nhieu';
      // this.settingService.settingChanges$.subscribe((event) => {
      //   if (event.settingId === 'cardChapterSize') {
      //     this.gridType = event.newValue;
      //     this.calcGirdSize();
          
      //   }
      // });
      this.calcGirdSize();

    }
  }
  ngAfterViewInit() {
  }
  SetUpHistory() {
    const comic = this.historyService.GetHistory(this.comic?.id!);
    if (comic) {
      this.history = comic.chapters!.map((chapter) => {
        return chapter.id;
      });
    }
  }

  onScrollChange(idx: number) {
    this.refreshChapterOptions(idx);
  }

  ngOnChanges(change: SimpleChanges) {    
    if (isPlatformBrowser(this.platformId)) {
      this.calcGirdSize();
    }
    if (!this.allchapters || this.allchapters.length == 0) {
      this.cd.markForCheck();
    }

    this.calDistance();
    this.refeshOption();
    this.curOptioneValue = 0;
    this.preLoadChapters = this.allchapters;
    this.asc = false;
    this.cd.markForCheck();
  }
  refeshOption() {
    const _length = Math.floor(((this.comic?.numChapter || this.allchapters.length) - 1) / this.distance + 1);
    this.options = Array.from(
      { length: _length },
      (_, i) => {
        return {
          label: `${i * this.distance} - ${(i + 1) * this.distance}`,
          value: this.asc ? i : _length - i - 1,
        };
      },
    )
    if (!this.asc) {
      this.options.reverse();
    }

    this.curOptioneValue = 0;
  }
  calcGirdSize() {
    const value = 1;
    if (window.innerWidth < config.GetScreenSize('sm')) {
      //sm breakpoint
      this.gridSize = 1 + value;
    } else if (window.innerWidth < config.GetScreenSize('xl')) {
      // xl break point
      this.gridSize = 2 + value;
    } else {
      this.gridSize = 3 + value;
    }

    return this.gridSize;
  }
  calDistance() {
    this.distance = 30;
    if (this.comic!.numChapter > 1000) {
      this.distance = 100;
    } else if (this.comic!.numChapter > 200) {
      this.distance = 50;
    }
  }
  @HostListener('window:resize', ['$event'])
  onWindowResize() {
    this.calcGirdSize();
  }
  onOrderChange($event: Event) {
    let checked = ($event.target as HTMLInputElement).checked;
    this.asc = checked;
    this.allchapters.sort((a, b) => {
      return checked ? a.slug - b.slug : b.slug - a.slug;
    });
    this.preLoadChapters = [...this.allchapters];
    this.refeshOption();
    this.cd.markForCheck();

  }
  onSearchChapter(e: any) {
    const value: string = e.target.value?.toLowerCase();
    if (!value) {
      this.preLoadChapters = this.allchapters;
      return;
    }
    this.preLoadChapters = this.allchapters.filter((chapter) =>
      chapter.title?.toLowerCase().includes(value),
    );
    this.cd.markForCheck();
  }


  refreshChapterOptions(curRow: number) {

    const optioneValue = Math.round(curRow * this.gridSize / this.distance);
    if (optioneValue !== this.curOptioneValue) {
      this.curOptioneValue = Math.min(optioneValue, this.options.length - 1);
      this.cd.markForCheck();
    }
  }

  selectChapterRange(id: number) {
    const value = (id * this.distance) / this.gridSize;
    this.loopScrollComponent.goToItem(value);
  }
  getChapterUrl(comic: Comic, chapter: Chapter): any {
    return this.urlService.getChapterDetailUrl(comic, chapter);
  }
}
