// Comic Card Container
.comic-container {
  @apply shadow-md h-full flex flex-col rounded-t-lg rounded-b-md overflow-hidden relative;
}

// Hot Tag Styles
.top-right {
  @apply bg-primary-200 m-1 transform rounded-md absolute top-0 right-0 z-10 text-white text-center flex items-center justify-center;
}

.hot-tag-text {
  @apply font-bold px-1.5 py-0.5 text-[0.7rem];
}

.hot-tag-animated {
  @apply animate-ping absolute font-bold p-1 text-[0.6rem] text-white;
}

// Card Container
.container-card-v1 {
  @apply relative w-full h-0 overflow-hidden transition-transform duration-300  hover:-translate-y-2;
  padding-bottom: 140%;
}

// Comic Link and Image
.comic-link {
  @apply flex h-full w-full;
}

.comic-image {
  @apply object-cover w-full absolute;
}

// Footer Overlay
.footer-card-v1 {
  @apply w-full  bg-gradient-to-t from-black to-black/20 absolute bottom-0 flex;
}

// Comic Info Section
.comic-info {
  @apply text-white text-sm p-1 w-full;
}

.comic-title {
  @apply font-semibold line-clamp-2;
}

.comic-author-view {
  @apply flex justify-between w-full;
}

.author-container {
  @apply flex items-center;
}

.author-text {
  @apply font-light text-xs line-clamp-1 dark:text-neutral-400;
}

// View Count Section
.view-count {
  @apply flex gap-1 items-center text-neutral-200 fill-slate-200;
}

.view-count-text {
  @apply font-normal text-[0.7rem] text-center uppercase;
}

// Card Footer
.card-footer {
  @apply px-0.5 py-1.5 flex justify-between text-[0.75rem] h-full dark:bg-neutral-800;

  &:hover {
    @apply dark:hover:bg-zinc-700 bg-gradient-to-t from-primary-50/40 to-white/80 dark:from-dark-bg dark:to-dark-bg/50;
  }
}

.chapter-title {
  @apply text-xs font-semibold dark:text-white;
}

.chapter-update {
  @apply text-end text-[0.75rem] inline text-neutral-700 dark:text-neutral-400;
}

// Event Footer
.text-footer {
  @apply p-1 text-white cursor-pointer font-bold flex-row text-center justify-between items-center text-[0.75rem] bg-slate-400 dark:bg-neutral-600;

  &:hover {
    @apply bg-red-500;
  }
}

// Placeholder Styles
.placeholder-image {
  @apply animate-pulse bg-neutral-300 dark:bg-neutral-700 relative w-full h-0 overflow-hidden;
  padding-bottom: 140%;
}

.placeholder-content {
  @apply flex justify-center items-center w-full h-full top-0 absolute;
}

.view-count-icon {
  @apply h-10 w-10 text-neutral-200 dark:text-neutral-600;
}
