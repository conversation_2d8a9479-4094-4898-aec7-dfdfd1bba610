import { 
  Component, 
  Input, 
  OnInit, 
  OnDestroy, 
  ChangeDetectionStrategy, 
  ChangeDetectorRef,
  ElementRef,
  ViewChild,
  Inject,
  PLATFORM_ID
} from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ImageService } from '@services/image.service';
import { Subscription } from 'rxjs';

@Component({
  selector: '[app-optimized-image]',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="image-container" [ngClass]="containerClass">
      <img
        #imageElement
        [src]="currentSrc"
        [alt]="alt"
        [loading]="loading"
        [class]="imageClass"
        [style.width]="width"
        [style.height]="height"
        (load)="onImageLoad()"
        (error)="onImageError()"
        [ngClass]="{
          'opacity-0': !imageLoaded,
          'opacity-100 transition-opacity duration-300': imageLoaded
        }"
      />
      
      <!-- Loading placeholder -->
      <div 
        *ngIf="!imageLoaded && showPlaceholder"
        class="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse flex items-center justify-center"
      >
        <div class="w-8 h-8 border-2 border-neutral-300 border-t-sky-500 rounded-full animate-spin"></div>
      </div>
      
      <!-- Error placeholder -->
      <div 
        *ngIf="hasError"
        class="absolute inset-0 bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center"
      >
        <svg class="w-12 h-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
          </path>
        </svg>
      </div>
    </div>
  `,
  styles: [`
    .image-container {
      position: relative;
      overflow: hidden;
    }
    
    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  `]
})
export class OptimizedImageComponent implements OnInit, OnDestroy {
  @Input() src!: string;
  @Input() alt = '';
  @Input() loading: 'lazy' | 'eager' = 'lazy';
  @Input() width?: string;
  @Input() height?: string;
  @Input() fallbackSrc = '/option2.png';
  @Input() imageClass = '';
  @Input() containerClass = '';
  @Input() showPlaceholder = true;
  @Input() preload = false;

  @ViewChild('imageElement') imageElement!: ElementRef<HTMLImageElement>;

  currentSrc = '';
  imageLoaded = false;
  hasError = false;
  private subscription?: Subscription;
  private intersectionObserver?: IntersectionObserver;

  constructor(
    private imageService: ImageService,
    private cdr: ChangeDetectorRef,
    private elementRef: ElementRef,
    @Inject(PLATFORM_ID) private platformId: object
  ) {}

  ngOnInit(): void {
    if (this.preload) {
      this.loadImage();
    } else if (isPlatformBrowser(this.platformId)) {
      this.setupIntersectionObserver();
    } else {
      // For SSR, load immediately
      this.currentSrc = this.src;
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  private setupIntersectionObserver(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.loadImage();
            this.intersectionObserver?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px' // Start loading 50px before the image comes into view
      }
    );

    this.intersectionObserver.observe(this.elementRef.nativeElement);
  }

  private loadImage(): void {
    if (!this.src) return;

    this.subscription = this.imageService.loadImageOptimized(this.src).subscribe({
      next: (objectUrl) => {
        this.currentSrc = objectUrl;
        this.cdr.detectChanges();
      },
      error: () => {
        this.onImageError();
      }
    });
  }

  onImageLoad(): void {
    this.imageLoaded = true;
    this.hasError = false;
    this.cdr.detectChanges();
  }

  onImageError(): void {
    this.hasError = true;
    this.imageLoaded = false;
    this.currentSrc = this.fallbackSrc;
    this.cdr.detectChanges();
  }

  // Method to retry loading
  retry(): void {
    this.hasError = false;
    this.imageLoaded = false;
    this.loadImage();
  }
}
