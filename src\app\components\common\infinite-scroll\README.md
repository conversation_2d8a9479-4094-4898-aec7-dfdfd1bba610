# Infinite Scroll Component

Component tối ưu để hiển thị danh sách items theo chiều ngang với infinite scroll, responsive design và sử dụng Tailwind CSS.

## Features

- ✅ **Horizontal Infinite Scroll**: Cuộn ngang với khả năng tải thêm dữ liệu
- ✅ **Responsive Design**: Tự động điều chỉnh kích thước theo màn hình
- ✅ **Auto Scroll**: Tự động cuộn với tốc độ tùy chỉnh
- ✅ **Performance Optimized**: Sử dụng Angular Signals và zone optimization
- ✅ **Smooth Animation**: Animation mượt mà với requestAnimationFrame
- ✅ **Navigation Controls**: Nút điều hướng với keyboard support
- ✅ **Progress Indicator**: Hiển thị progress theo tùy chọn
- ✅ **TypeScript Generic**: Type-safe với TypeScript generics
- ✅ **SSR Compatible**: Hỗ trợ Server-Side Rendering

## Usage

### 1. Import Component

```typescript
import { InfiniteScrollComponent } from './components/common/infinite-scroll/infinite-scroll.component';

@Component({
  imports: [InfiniteScrollComponent],
  // ...
})
export class YourComponent {
  // ...
}
```

### 2. Basic Usage

```html
<app-infinite-scroll 
  [items]="comicList"
  [itemWidth]="200"
  [gap]="16"
  (itemClick)="onComicClick($event)"
  (loadMore)="loadMoreComics()">
</app-infinite-scroll>
```

### 3. Advanced Usage

```html
<app-infinite-scroll 
  [items]="popularComics"
  [itemWidth]="250"
  [gap]="20"
  [autoScroll]="true"
  [autoScrollSpeed]="50"
  [showProgress]="true"
  [showNavigation]="true"
  (itemClick)="navigateToComic($event)"
  (loadMore)="loadMorePopularComics()"
  class="my-8">
</app-infinite-scroll>
```

### 4. Component TypeScript

```typescript
export class ComicListComponent {
  comicList: Comic[] = [];
  
  onComicClick(comic: Comic) {
    this.router.navigate(['/comic', comic.id]);
  }
  
  loadMoreComics() {
    this.comicService.getMoreComics().subscribe(newComics => {
      this.comicList = [...this.comicList, ...newComics];
    });
  }
}
```

## Props/Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `items` | `T[]` | `[]` | Danh sách items để hiển thị |
| `itemWidth` | `number` | `200` | Chiều rộng mỗi item (px) |
| `gap` | `number` | `16` | Khoảng cách giữa các items (px) |
| `autoScroll` | `boolean` | `false` | Tự động cuộn |
| `autoScrollSpeed` | `number` | `30` | Tốc độ auto scroll (ms) |
| `showProgress` | `boolean` | `false` | Hiển thị progress indicator |
| `showNavigation` | `boolean` | `true` | Hiển thị nút điều hướng |

## Events/Outputs

| Event | Type | Description |
|-------|------|-------------|
| `itemClick` | `EventEmitter<T>` | Emit khi click vào item |
| `loadMore` | `EventEmitter<void>` | Emit khi cần tải thêm data |

## Item Structure

Component expects items to have these properties (all optional):

```typescript
interface ScrollItem {
  image?: string;     // URL ảnh thumbnail
  title?: string;     // Tiêu đề chính
  subtitle?: string;  // Tiêu đề phụ
  badge?: string;     // Badge/tag hiển thị
  [key: string]: any; // Các properties khác
}
```

## Responsive Breakpoints

- **Mobile** (`< 640px`): Item width * 0.8, show 2 items per row
- **Tablet** (`< 1024px`): Item width * 0.9, show 3 items per row  
- **Desktop** (`>= 1024px`): Original item width

## Performance Features

- **Zone Optimization**: Auto scroll runs outside Angular zone
- **Throttled Events**: Scroll events throttled để tránh spam
- **Memory Management**: Proper cleanup khi component destroy
- **SSR Safety**: Platform checks để tránh lỗi SSR
- **Computed Properties**: Sử dụng signals để optimize re-renders

## CSS Classes

Component sử dụng Tailwind CSS với các classes responsive:

```html
<!-- Container -->
<div class="relative w-full overflow-hidden rounded-lg bg-white dark:bg-gray-800">
  
<!-- Scroll container -->
<div class="flex gap-4 overflow-x-auto scrollbar-hide">

<!-- Item -->
<div class="flex-shrink-0 bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow">
```

## Example trong Comic App

```typescript
// comic-list.component.ts
@Component({
  template: `
    <div class="space-y-8">
      <!-- Hot Comics -->
      <section>
        <h2 class="text-2xl font-bold mb-4">Truyện Hot</h2>
        <app-infinite-scroll 
          [items]="hotComics"
          [itemWidth]="180"
          [gap]="12"
          [autoScroll]="true"
          [autoScrollSpeed]="60"
          [showProgress]="true"
          (itemClick)="navigateToComic($event)"
          (loadMore)="loadMoreHotComics()">
        </app-infinite-scroll>
      </section>
      
      <!-- New Comics -->
      <section>
        <h2 class="text-2xl font-bold mb-4">Truyện Mới</h2>
        <app-infinite-scroll 
          [items]="newComics"
          [itemWidth]="200"
          [gap]="16"
          (itemClick)="navigateToComic($event)"
          (loadMore)="loadMoreNewComics()">
        </app-infinite-scroll>
      </section>
    </div>
  `
})
export class ComicListComponent {
  hotComics: Comic[] = [];
  newComics: Comic[] = [];
  
  navigateToComic(comic: Comic) {
    this.router.navigate(['/truyen-tranh', comic.slug]);
  }
  
  loadMoreHotComics() {
    // Load more hot comics logic
  }
  
  loadMoreNewComics() {
    // Load more new comics logic  
  }
}
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Dependencies

- Angular 17+ với Signals API
- RxJS 7+
- Tailwind CSS 3+
