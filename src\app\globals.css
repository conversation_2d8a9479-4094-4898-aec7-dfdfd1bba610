@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark theme variables */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --dark-bg: #111827;
  --dark-text: #ffffff;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Base styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Dark theme support */
.dark {
  background-color: var(--dark-bg);
  color: var(--dark-text);
}

.light {
  background-color: var(--background);
  color: var(--foreground);
}

/* Container styles */
.main {
  @apply min-h-screen;
}

/* Comic specific styles */
.home-content {
  @apply md:container w-full mx-auto;
}

.carousel-landing {
  @apply mt-0 mb-0 flex w-full h-full pb-48 xs:pb-56 sm:pb-60 lg:pb-[18%] relative;
}

.block-title {
  @apply text-xl uppercase font-extrabold text-white flex min-h-full px-2 text-center items-center justify-center mt-1;
}

/* Navigation styles */
.nav-header {
  @apply sticky top-0 z-50 bg-white dark:bg-dark-bg border-b border-gray-200 dark:border-gray-700;
}

.nav-container {
  @apply container mx-auto px-4 py-3 flex items-center justify-between;
}

.nav-logo {
  @apply flex items-center space-x-2;
}

.nav-actions {
  @apply flex items-center space-x-4;
}

.nav-main {
  @apply bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}

.nav-mobile {
  @apply container mx-auto px-4 py-2 flex items-center justify-between;
}

.nav-home {
  @apply flex items-center space-x-2 text-gray-700 dark:text-gray-300;
}

.nav-icon {
  @apply w-5 h-5;
}

.nav-toggle {
  @apply p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700;
}

/* Footer styles */
.footer-container {
  @apply bg-gray-100 dark:bg-gray-900 mt-auto;
}

.footer-content {
  @apply container mx-auto px-4 py-8;
}

.footer-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

.footer-brand {
  @apply space-y-4;
}

.brand-logo {
  @apply flex items-center space-x-2;
}

.logo-image {
  @apply h-8 w-auto;
}

.brand-tagline {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.brand-description {
  @apply text-sm text-gray-600 dark:text-gray-400 leading-relaxed;
}

/* Comic styles */
.comic-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4;
}

.comic-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow;
}

.comic-image {
  @apply w-full aspect-[3/4] object-cover;
}

.comic-title {
  @apply p-3 text-sm font-medium text-gray-900 dark:text-white;
}

/* Announcement styles */
.announcement-card {
  @apply relative flex items-center px-1 bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-sm hover:shadow-lg overflow-hidden;
}

.announcement-icon-bg {
  @apply flex items-center justify-center w-8 h-8 rounded-full relative z-10;
}

.announcement-icon {
  @apply w-5 h-5 text-blue-600 dark:text-blue-400;
}

.announcement-pulse {
  @apply absolute inset-0 w-10 h-10 bg-blue-600 dark:bg-blue-400 rounded-full animate-ping opacity-30;
}

.announcement-content {
  @apply flex-1 px-3 py-2;
}

.announcement-text {
  @apply text-sm text-gray-700 dark:text-gray-300 font-medium;
}
