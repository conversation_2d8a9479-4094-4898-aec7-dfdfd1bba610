<!-- Modern Comic Detail Popup -->
<div
  [@ShowAnimation]
  class="popup-detail-container"
>
  <!-- Background Gradient -->
  <div class="popup-background"></div>

  <!-- Main Content -->
  <div class="popup-content">
    <!-- Header Section -->
    <div class="popup-header">
      <div class="title-section">
        <p class="comic-title">{{ comic.title }}</p>
        <!-- <div class="status-badge">
          @if (comic.status === 0) {
            <div class="status-indicator ongoing">
              <div class="status-dot"></div>
              <span class="status-text"><PERSON><PERSON> tiến hành</span>
            </div>
          } @else {
            <div class="status-indicator completed">
              <svg class="status-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span class="status-text"><PERSON><PERSON><PERSON> thành</span>
            </div>
          }
        </div> -->
      </div>

      <!-- Author -->
      <div class="author-section">
        <svg class="author-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
        <span class="author-name">{{ comic.author || 'Đang cập nhật' }}</span>
      </div>
    </div>

    <!-- Genres Section -->
    <div class="genres-section" *ngIf="comic?.genres?.length">
      <div class="genres-container">
        <span
          *ngFor="let tag of comic.genres; index as i"
          class="genre-tag"
          [class.primary-genre]="i === 0"
        >
          {{ tag.title }}
        </span>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="stats-section-compact">
      <div class="stat-compact">
        <svg class="stat-icon" viewBox="0 0 24 24" fill="currentColor" width="18" height="18">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        <span class="stat-value">{{ comic.rating || 'N/A' }}</span>
      </div>
      <div class="stat-compact">
        <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" width="18" height="18">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
          <circle cx="12" cy="12" r="3"/>
        </svg>
        <span class="stat-value">{{ comic.viewCount | numeral }}</span>
      </div>
      <div class="stat-compact">
        <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" width="18" height="18">
          <path d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z"/>
          <path d="M9 9h6M9 12h6M9 15h6"/>
        </svg>
        <span class="stat-value">{{ comic.chapters?.length || 0 }}</span>
      </div>
    </div>

    <!-- Description Section -->
    <div class="description-section">
      <div class="description-header">
        <svg class="description-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
        <span class="description-title">Mô tả</span>
      </div>
      <p
        class="description-text"
        [innerHTML]="comic.description | fillDescription : comic.id : comic.title : comic.url"
      ></p>
    </div>

   
  </div>
</div>