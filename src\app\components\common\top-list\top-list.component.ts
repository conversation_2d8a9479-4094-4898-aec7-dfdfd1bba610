import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Input, OnInit, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic, IServiceResponse, TopType } from '@schema';
import { ComicService } from '@services/comic.service';
import { UrlService } from '@services/url.service';
import { BaseComponent } from '../base/component-base';

@Component({
  selector: 'div[app-top-list]',
  templateUrl: './top-list.component.html',
  styleUrl: './top-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink, NumeralPipe],
})
export class TopListComponent extends BaseComponent implements OnInit {

  @Input() listTopComics!: Comic[];
  topType!: number;

  constructor(
    private ComicService: ComicService,
    @Inject(PLATFORM_ID) override platformId: object,
    private urlService: UrlService,
    private cd: ChangeDetectorRef
  ) { super(platformId); }

  ngOnInit(): void {
    this.getTopComic(TopType.Day);
    //  this.runInBrowser(() => {
    // });
  }
  getComicDetailUrl(comic: Comic): string|any[]|import("@angular/router").UrlTree|null|undefined {
    return this.urlService.getComicDetailUrl(comic);
  }
  getTopComic(type: TopType): void {
    this.topType = type;
    this.ComicService.getTopComics(this.topType).subscribe((res: IServiceResponse<Comic[]>) => {
      if (!res.data) return;
      this.listTopComics = res.data;
      this.cd.markForCheck();
    });
  }

  // TrackBy function for performance optimization
  trackByComicId = (_index: number, comic: Comic): number => {
    return comic.id;
  };

  // Get rank class based on position
  getRankClass(rank: number): string {
    switch (rank) {
      case 1:
        return 'rank-gold';
      case 2:
        return 'rank-silver';
      case 3:
        return 'rank-bronze';
      default:
        return 'rank-default';
    }
  }
}
