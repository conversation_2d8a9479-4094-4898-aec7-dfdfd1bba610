<ng-container>
  <div app-breadcrumb
    class="z-10 my-2 md:container mx-auto flex"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: '<PERSON>ịch sử', url: '/lich-su' }
    ]"
  >
  </div>

  <div class="mt-6 md:container mx-auto">
    <div class="text-start" *ngIf="isLoading; else loadedContent">
      <div app-spinner [sizeSpinner]="'40'"></div>
    </div>
    <ng-template #loadedContent>
      <div app-grid-comic
        *ngIf="comics.length > 0; else empty"
        [listComics]="comics"
        [nPreview]="comicPerPage"
        [title]="'Truyện đã xem'"
       
      >
        <ng-template #iconTemplate>
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-1"
          >
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"></circle>
            <path
              d="M12 8V12L14.5 14.5"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </ng-template>
        <ng-template #actionTemplate let-comic="comic">
          <button
            (click)="onRemoveSelectedComics([comic.id])"
            class="bg-red-500 hover:bg-primary-200 m-1.5 rounded-md absolute top-0 right-0 z-10 text-white"
          >
            <svg class="mx-1.5 my-1 size-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </ng-template>
      </div>

      <div 
        app-pagination
        *ngIf="totalpage > 1 && !ssr()"
        [totalpage]="totalpage"
        [currentPage]="page"
        (OnChange)="onChangePage($event)"
      >
      </div >
      <ng-template #empty>
        <div class="w-1/2 mt-20 lg:min-h-1/2 lg:w-40 flex justify-center items-center mx-auto">
          <div app-empty [message]="'Không có truyện đã xem'"></div>
        </div>
      </ng-template>
    </ng-template>
  </div>
</ng-container>
