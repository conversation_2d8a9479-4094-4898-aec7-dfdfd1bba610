<!-- Author Comics Page -->
<div class="author-comics-container">
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <!-- <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb> -->
  </div>

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="author-info">
        <div class="author-avatar">
          <svg class="avatar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
            <circle cx="12" cy="7" r="4" />
          </svg>
        </div>
        <div class="author-details">
          <h1 class="author-name">{{ author }}</h1>
          <p class="author-subtitle">T<PERSON>c g<PERSON>ả truyện tranh</p>
          <div class="author-stats" *ngIf="!isLoading && !hasError">
            <div class="stat-item">
              <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z"
                />
                <path d="M9 9h6M9 12h6M9 15h6" />
              </svg>
              <span class="stat-value">{{ listComics.length }}</span>
              <span class="stat-label">Tác phẩm</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Content Section -->
  <div class="content-section">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <div  app-spinner [sizeSpinner]="'40'"></div >
      <p class="loading-text">Đang tải danh sách truyện...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError && !isLoading" class="error-container">
      <div class="error-content">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
        <h3 class="error-title">Có lỗi xảy ra</h3>
        <p class="error-message">{{ errorMessage }}</p>
        <button class="retry-btn" (click)="onRetry()">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="23 4 23 10 17 10" />
            <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" />
          </svg>
          Thử lại
        </button>
      </div>
    </div>

    <!-- Comics Grid -->
    <div *ngIf="!isLoading && !hasError" class="comics-section">
      <!-- Comics List -->
      <div *ngIf="listComics.length > 0; else emptyState">
        <div class="section-header">
          <h2 class="section-title">
            <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z"
              />
              <path d="M9 9h6M9 12h6M9 15h6" />
            </svg>
            Tác phẩm của {{ author }}
          </h2>
          <div class="result-count">
            <span class="count-number">{{ listComics.length }}</span>
            <span class="count-text">tác phẩm</span>
          </div>
        </div>

        <!-- Grid Component -->
        <div class="comics-grid">
          <div
            app-grid-comic
            [listComics]="listComics"
            [nPreview]="listComics.length"
          ></div>
        </div>
      </div>

      <!-- Empty State -->
      <ng-template #emptyState>
        <div class="empty-container">
          <!-- <app-empty
            [title]="'Không tìm thấy truyện'"
            [message]="'Tác giả ' + author + ' chưa có tác phẩm nào hoặc thông tin tác giả không chính xác.'"
            [showButton]="true"
            [buttonText]="'Về trang chủ'"
            (buttonClick)="router.navigate(['/'])"
          ></app-empty> -->
        </div>
      </ng-template>
    </div>
  </div>
</div>
