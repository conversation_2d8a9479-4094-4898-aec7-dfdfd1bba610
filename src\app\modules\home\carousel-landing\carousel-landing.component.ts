import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  HostListener,
  Inject,
  NgZone,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  signal,
  ViewChild
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { UrlService } from '@services/url.service';
import { Subscription, timer } from 'rxjs';
import { SwiperComponent } from 'src/app/modules/home/<USER>/page/swiper/swiper.component';
@Component({
  selector: 'div[app-carousel-landing]',
  templateUrl: './carousel-landing.component.html',
  styleUrl: './carousel-landing.component.scss',
  animations: [
    trigger('formAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(100%)' })),
      state('*', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [animate('200ms ease-out')]),
      transition('* => void', [animate('200ms ease-in')]),
    ]),
  ],
  standalone: true,
  imports: [
    // SwiperComponent,
    // NgOptimizedImage,
    CommonModule,
    RouterLink,
    SpinnerComponent,
    ComicDescriptionPipe,
    NumeralPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class CarouselLandingComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  //   e.nativeElement.classList.add('invisible');
  // if (isNext && value <= this.grid()) {
  //   e.nativeElement.classList.remove('invisible');
  // } else if (!isNext && value >= 1 && value <= this.grid() + 1) {
  //   e.nativeElement.classList.remove('invisible');
  // }
  // });

  startX = 0;
  curTransformX = signal<number>(0);
  preTransfromX = signal<number>(0);
  onMouseDown(event: MouseEvent): void {
    event.preventDefault(); // Ngăn click nếu đang kéo
    this.isDragging = true;
    this.startX = event.clientX;
    this.preTransfromX.set(this.curTransformX());
  }
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;
    const deltaX = event.clientX - this.startX;
    // this.preTransfromX.set(this.curTransformX());
    this.curTransformX.set(this.preTransfromX() + deltaX);
  }

  @HostListener('document:mouseup', ['$event'])
  onMouseUp(event: MouseEvent): void {
    event.preventDefault(); // Ngăn click nếu đang kéo    
    // event.stopPropagation();
    this.isDragging = false;
    this.inertiaScroll();
  }
  isDragging = false;
  velocity = 0;
  animationFrame: number | null = null;

  inertiaScroll() {
    const friction = 0.85;
    this.velocity =5;

    const step = () => {
      this.velocity *= friction;
      this.curTransformX.set(this.curTransformX() + this.velocity);
      if (Math.abs(this.velocity) > 0.5 ) {
        this.animationFrame = requestAnimationFrame(step);
      }
    };

    step();
  }
  getComicUrl(comic: Comic): any {
    return this.urlService.getComicDetailUrl(comic);
  }
  // Component state using signals for better reactivity
  carouselItems = signal<Comic[][]>([]);
  lastTime = signal<number>(0);
  isTransitioning = signal<boolean>(false);
  hoverComic = signal<Comic | undefined>(undefined);
  _state = signal<string>('out');
  typeUI = signal<number>(0);
  currentIdx = signal<number>(0);
  attrs = signal<any[]>([]);
  subscription?: Subscription;

  // Performance optimizations
  private readonly AUTO_SLIDE_INTERVAL = 5000;
  private readonly INITIAL_DELAY = 3000;
  private readonly TRANSITION_THROTTLE = 500;
  private debouncedNext!: Function;
  private debouncedPrev!: Function;

  @ViewChild(SwiperComponent) swiperComponent!: SwiperComponent;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    @Inject(PLATFORM_ID) override platformId: object,
    private comicService: ComicService,
    private urlService: UrlService
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }
  readonly classes = [
    "item-1",
    "item-2",
    "item-3",
    "item-4",
    "item-5",
  ];
  // Computed properties for optimal performance
  hasCarouselItems = computed(() => this.carouselItems().length > 0);
  isDesktop = computed(() => this.typeUI() === 0);
  isMobile = computed(() => this.typeUI() === 1);
  shouldShowHoverDetails = computed(() =>
    !!this.hoverComic() && this.isDesktop()
  );


  // TrackBy functions for ngFor optimization
  trackByCarouselIndex = (index: number): number => index;
  trackByComicId = (index: number, comic: Comic): number => comic.id;
  ngOnInit(): void {
    this.ngZone.runOutsideAngular(() => {
      this.runInBrowser(() => {
        this.setupInitialDelay();
      });
    })
    this.loadRecommendedComics();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  private setupDebouncedMethods(): void {
    this.debouncedNext = this.debounce(this.performNext.bind(this), 100);
    this.debouncedPrev = this.debounce(this.performPrev.bind(this), 100);
  }

  private setupInitialDelay(): void {
    this.addSubscription(
      timer(this.INITIAL_DELAY).pipe(this.takeUntilDestroy()).subscribe(() => {
        this.resetAutoSlide();
      })
    );
  }

  public getLeftPosition(index: number): string {
    let value = index;
    value = (index + this.currentIdx()) % this.carouselItems().length;


    return `calc(${(value) * 100}% / var(--carousel-grid) - 100%/var(--carousel-grid)/2)`;
  }

  private loadRecommendedComics(): void {
    this.addSubscription(
      this.comicService.getRecommendComics().pipe(this.takeUntilDestroy()).subscribe((res: any) => {
        if (!res.data) return;

        if (this.isServer) {
          // const chunks = this.chunk<Comic>(res.data, 4)
          // this.carouselItems.set([...chunks, ...chunks.slice(1, 2)]);
        } else {
        }
        const chunks = this.chunk<Comic>(res.data, 1)
        this.carouselItems.set([...chunks]);


        this.safeMarkForCheck();
      })
    );
  }
  chunk<T>(array: T[], size: number): T[][] {
    const result: T[][] = [];
    let i = 0;
    let x = 0;
    while (i < array.length) {
      // Slice the array from the current index to the next 'size' elements
      if (x % 2 == 0) {
        result.push(array.slice(i, i + size)); // Add the chunk to the result array
        i += size;
      }
      else {
        result.push([array[i]]); // Add the chunk to the result array
        i++;
      }
      x++;
    }


    return result; // Return the array of chunks
  }

  OnComicLeave(): void {
    this._state.set('out');
    this.startAutoSlide();
  }

  @HostListener('document:visibilitychange', ['$event'])
  onDocumentVisibilityChange(): void {
    this.runInBrowser(() => {
      if (document.hidden) {
        this.stopAutoSlide();
      } else {
        this.startAutoSlide();
      }
    });
  }

  OnComicHover(comic: Comic): void {
    // if (this.hoverComic() === comic) return;
    // this.stopAutoSlide();
    // this.hoverComic.set(comic);
    // this.safeMarkForCheck();
  }

  private startAutoSlide(): void {
    this.stopAutoSlide();
    this.ngZone.runOutsideAngular(() => {
      // this.subscription = interval(this.AUTO_SLIDE_INTERVAL).pipe(this.takeUntilDestroy()).subscribe(() => this.prev())
    })

  }

  private stopAutoSlide(): void {
    this.subscription?.unsubscribe();
  }

  next(step = 1): void {
    this.debouncedNext(step);
  }

  prev(step = 1): void {
    this.debouncedPrev(step);
  }

  private performNext(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(true);
    this.swiperComponent?.next();
  }

  private performPrev(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(false);
    this.swiperComponent?.prev();
  }

  private canTransition(): boolean {
    const now = Date.now();
    return now - this.lastTime() >= this.TRANSITION_THROTTLE;
  }

  private updateLastTime(): void {
    this.lastTime.set(Date.now());
  }

  private performRecalculate(isNext = true): void {
    this.runInBrowser(() => {
      this.updateElementVisibility(isNext);
      this.updateCurrentIndex(isNext);
      // this.updateElementPositions();
    });
  }

  private updateElementVisibility(isNext: boolean): void {
    // this.carouselEles.forEach((e: ElementRef, index) => {
    //   const value = (index + this.currentIdx()) % this.carouselEles.length;
    //   e.nativeElement.classList.add('invisible');

    // if (isNext && value <= this.grid()) {
    //   e.nativeElement.classList.remove('invisible');
    // } else if (!isNext && value >= 1 && value <= this.grid() + 1) {
    //   e.nativeElement.classList.remove('invisible');
    // }
    // });
  }

  private updateCurrentIndex(isNext: boolean): void {
    console.log('updateCurrentIndex', isNext);

    const direction = isNext ? 1 : -1;
    this.currentIdx.update(current =>
      (current + direction + this.carouselItems().length) % this.carouselItems().length
    );
  }


  private resetAutoSlide(): void {
    this.startAutoSlide();
  }
  SlideChange(direct: number): void {
    this.performRecalculate(direct > 0);
  }

}
