import {
    animate,
    keyframes,
    style,
    transition,
    trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Input } from '@angular/core';
import { ComicDescriptionPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { IPopupComponent } from 'src/app/core/interface';
@Component({
  selector: '[app-popup-detail-comic]',
  templateUrl: './popup-detail-comic.component.html',
  styleUrl: './popup-detail-comic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, ComicDescriptionPipe,NumeralPipe],
  animations: [
    trigger('ShowAnimation', [
      transition(':enter', [
        animate(200, keyframes([
          style({ opacity: 0, offset: 0 }),
          style({ opacity: 0.5, offset: 0.5 }),
          style({ opacity: 1, offset: 1 }),
        ])),
      ]),
    ]),
  ],
  standalone: true
})
export class PopupDetailComicComponent implements IPopupComponent {
  @Input() comic: Comic = {} as Comic;

  constructor(
    private element: ElementRef<any>,
    private cd: ChangeDetectorRef,
  ) { }
  setVisible(isVisible: boolean): void {
    
  }
  show(object: any): Promise<any> {
    const { comic } = object || {}
    this.comic = comic
    this.cd.detectChanges();
    return new Promise((resolve) => {
      resolve({})
    })
  }
  // ngOnInit() { }
  @HostListener('window:mousemove', ['$event'])
  onmousemove(event: MouseEvent) {
    if (
      event.clientY + this.element.nativeElement.offsetHeight >
      window.innerHeight
    ) {
      this.element.nativeElement.style.top =
        event.clientY - this.element.nativeElement.offsetHeight - 20 + 'px';
    } else {
      this.element.nativeElement.style.top = event.clientY + 20 + 'px';
    }
    const pos = Math.min(
      Math.max(event.clientX - 0.5 * this.element.nativeElement.offsetWidth, 0),
      window.innerWidth - this.element.nativeElement.offsetWidth
    );
    this.element.nativeElement.style.left = pos + 'px';
  }

}
