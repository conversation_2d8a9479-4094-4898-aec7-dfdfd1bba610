// AI Assistant Page - Tailwind CSS Implementation
// Only keeping animations and complex styles that can't be done with Tailwind classes

// Keyframe animations
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-0.5rem);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-0.5rem);
  }
  70% {
    transform: translateY(-0.25rem);
  }
  90% {
    transform: translateY(-0.125rem);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
}

// Character animations
.character-image {
  animation: float 3s ease-in-out infinite;
}

.mini-character {
  &.char-1 {
    animation: float 4s ease-in-out infinite;
  }

  &.char-2 {
    animation: float 3.5s ease-in-out infinite 0.5s;
  }

  &.char-3 {
    animation: float 4.5s ease-in-out infinite 1s;
  }
}

// CTA character animations
.cta-character-left {
  animation: float 4s ease-in-out infinite;
}

.cta-character-right {
  animation: float 3s ease-in-out infinite 1s;
}

// Speech bubble arrow (complex CSS that can't be done with Tailwind)
.speech-bubble {
  &::before {
    content: '';
    position: absolute;
    top: -0.5rem;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 1rem;
    height: 1rem;
    background: white;

    .dark & {
      background: #374151;
    }
  }
}

// Hover effects with transforms and animations
.cta-button {
  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.cta-button-large {
  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.feature-card {
  &:hover {
    transform: translateY(-0.5rem);
    animation: bounce 0.6s ease-in-out;
  }
}

.step-card {
  &:hover {
    transform: translateY(-0.25rem);
  }
}

.demo-question {
  &:hover {
    transform: translateY(-0.25rem);
    animation: pulse 1s ease-in-out infinite;
  }
}

