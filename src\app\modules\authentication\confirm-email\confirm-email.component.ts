import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
    selector: '[app-confirm-email]',
    templateUrl: './confirm-email.component.html',
    styleUrl: './confirm-email.component.scss',
    standalone: false
})
export class ConfirmEmailComponent implements OnInit {
  countdownsend = 0;
  countdownInterval: any
  mssg = '';
  constructor(private accountService: AccountService, private toast: ToastService, private route: ActivatedRoute) {


  }
  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.mssg = params['mssg'] || 'Hệ thống đã gửi email bạn đăng kí để xác thực tài khoản, xin vui lòng xác thực tài khoản';

    })
  }
  sendEmail() {
    if (this.countdownsend == 0) {
      const info = this.accountService.GetUserDeconfirm();
      this.accountService.SendEmailConfirm(info['email'], info['id']).subscribe((res: any) => {

        this.countdownsend = 60;
        this.countdownInterval = setInterval(() => {
          this.countdownsend--;
          if (this.countdownsend <= 0) {
            clearInterval(this.countdownInterval);
          }
        }, 1000)
        if (res.status === 1) {
          this.toast.show(ToastType.Success, res.message);

        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      });
    }

  }
}
