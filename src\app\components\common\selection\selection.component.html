<div class="relative flex" (appClickOutside)="ClickOutside()">
  <button
    #SelectionButton
    type="button"
    class="relative group grid border border-gray-300 grid-cols-[1fr_1rem] w-full min-w-fit px-2 rounded focus:outline-primary-100 font-medium dark:bg-neutral-800"
  >
    <span class="text-center truncate text-sm group-focus:text-primary-100">{{
      this.options![idx].label
    }}</span>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="w-5 h-5 my-auto "
      viewBox="0 0 24 24"
    >
      <path
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="m8 9l4-4l4 4m0 6l-4 4l-4-4"
      ></path>
    </svg>
  </button>
  <ul
    class="flex flex-col absolute border mt-1 w-full max-h-64 overflow-y-auto scrollbar-style-1 z-50 left-0 rounded-md shadow-md py-1 focus:outline-none top-full bg-white dark:bg-neutral-800 dark:border-neutral-500"
    [ngClass]="{
      hidden: !isVisible
    }"
  >
    <li
      *ngFor="let option of options"
      (click)="selectOption(option.value)"
      class=" cursor-pointer text-left mx-1 px-1 rounded-md
      {{
        option.value === value
          ? 'text-primary-100  border-primary-100  hover:bg-primary-100 hover:text-white'
          : 'hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-secondary-100 dark:hover:text-white'
      }}"
    >
      <span class="truncate">
        <label class="custom-radio flex items-center cursor-pointer">
          <span
            class="size-3 shrink-0 border rounded-full mr-2 transition-colors
                      {{
              option.value === value
                ? 'border-white bg-primary-100 '
                : 'border-neutral-400'
            }}"
          ></span>
          {{ option.label }}
        </label>
      </span>
    </li>
  </ul>
</div>
