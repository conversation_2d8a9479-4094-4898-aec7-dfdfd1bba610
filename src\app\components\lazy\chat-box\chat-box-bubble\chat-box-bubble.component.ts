import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ComponentRef, On<PERSON><PERSON>roy, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { <PERSON><PERSON> } from '@schema';
import { ChatBoxComponent } from '../chat-box.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'div[app-chat-box-bubble]',
  templateUrl: './chat-box-bubble.component.html',
  styleUrl: './chat-box-bubble.component.scss',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],

})
export class ChatBoxBubbleComponent implements OnDestroy {
  IsVisible = true;
  @ViewChild('chatbox', { read: ViewContainerRef }) container!: ViewContainerRef;
  chatBoxComponentRef!: ComponentRef<ChatBoxComponent>;
  private hideChatSubscription?: Subscription;

  async loadHeavyComponent() {
    const { ChatBoxComponent } = await import('../chat-box.component');
    if (this.chatBoxComponentRef) {
      if (!this.chatBoxComponentRef.hostView.destroyed) return;
    }
    this.container.clear();
    
    this.chatBoxComponentRef = this.container.createComponent(ChatBoxComponent);
    
    // Clean up previous subscription
    if (this.hideChatSubscription && !this.hideChatSubscription.closed) {
      this.hideChatSubscription.unsubscribe();
    }
    
    this.hideChatSubscription = this.chatBoxComponentRef.instance.hideChatChange.subscribe((value) => this.closeChat());
  }
  public isChapterRoute = false;
  public static Instance: ChatBoxBubbleComponent | null = null;

  constructor(
    private cd: ChangeDetectorRef,
  ) {
    ChatBoxBubbleComponent.Instance = this;

  }
  ngOnDestroy() {
    // Clean up subscription
    if (this.hideChatSubscription && !this.hideChatSubscription.closed) {
      this.hideChatSubscription.unsubscribe();
    }
    
    // Clean up component reference
    if (this.chatBoxComponentRef && !this.chatBoxComponentRef.hostView.destroyed) {
      this.chatBoxComponentRef.destroy();
    }
    
    ChatBoxBubbleComponent.Instance = null;
  }

  public SetVisible(isShow: boolean) {
    this.IsVisible = isShow
    this.cd.detectChanges();
  }
  closeChat() {
    this.SetVisible(true);
    this.chatBoxComponentRef.instance.setVisible(false)
  }
  showChat(channel = Chanel.Bot) {
    this.SetVisible(false);
    this.loadHeavyComponent().then(() => this.chatBoxComponentRef.instance.show({ channel }));
  }
}
