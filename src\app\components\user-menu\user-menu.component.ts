import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ComponentRef, computed, DestroyRef, inject, Inject, PLATFORM_ID, signal, ViewChild, ViewContainerRef } from '@angular/core';
import { BaseComponent } from '@components/common/base/component-base';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { IPopupComponent } from 'src/app/core/interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';


@Component({
  selector: 'div[app-user-menu]',
  templateUrl: './user-menu.component.html',
  styleUrl: './user-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, ClickOutsideDirective]
})
export class UserMenuComponent extends BaseComponent {
  private readonly destroyRef = inject(DestroyRef);

  user: IUser | undefined = undefined;
  isAuthenticated = false;
  isOpenSignal = signal(false);
  isOpen = computed(() => this.isOpenSignal());
  @ViewChild('userMenu', { read: ViewContainerRef }) container!: ViewContainerRef;
  usermenudropdown!: ComponentRef<IPopupComponent>;

  constructor(private accountService: AccountService, 
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(platformId);
  }

  ngOnInit(): void {
    this.accountService.GetLocalUser().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((user) => {
      this.user = user;
      this.isAuthenticated = !!user;
    });
  }

  async toggleMenu(): Promise<void> {
    this.isOpenSignal.set(!this.isOpenSignal());
    await this.loadHeavyComponent();
    if(this.isOpen())
    {
      this.usermenudropdown.instance.show({user: this.user}).then((res) => {
        this.isOpenSignal.set(res.isVisible);
      });
      return
    }
    this.usermenudropdown.instance.setVisible(false);
    
  }

  async closeMenu(): Promise<void> {
    if(!this.isOpen()) return;
    await this.loadHeavyComponent();
    this.isOpenSignal.set(false);
    this.usermenudropdown.instance.setVisible(this.isOpen());
    
    
  }
  override async loadHeavyComponent() {
    if (this.usermenudropdown && !this.usermenudropdown.hostView.destroyed) return;
    const { UserMenuDropdownComponent } = await import('./components/user-menu-dropdown.component');
    this.container.clear();
    this.usermenudropdown = this.container.createComponent(UserMenuDropdownComponent);
  }
}
