import { NgModule } from '@angular/core';
import {
  BrowserModule,
  provideClientHydration,
  withIncrementalHydration,
  withNoHttpTransferCache
} from '@angular/platform-browser';

import { HTTP_INTERCEPTORS, provideHttpClient, withFetch, withInterceptorsFromDi } from '@angular/common/http';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

import { CommonModule } from '@angular/common';
import { provideAnimations } from '@angular/platform-browser/animations';
import { AdComponent } from '@components/common/ad/ad.component';
import { LoadingBarComponent } from '@components/common/loading-bar/loading-bar.component';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { AuthInterceptor } from './core/http-interceptors/auth-interceptor';
import { CacheInterceptor } from './core/http-interceptors/cache-interceptor';
import { ForwardHostInterceptor } from './core/http-interceptors/forward-host.interceptor';
import { AdsModule } from './shared/ads.module';

@NgModule({
  declarations: [
    AppComponent,

  ],
  bootstrap: [AppComponent],
  imports: [BrowserModule,
    CommonModule,
    AppRoutingModule,
    AdsModule,
    AdComponent,
    LoadingBarComponent,
    // GenreCatagoriesComponent
  ],

  providers: [
    provideAnimations(),
    provideClientHydration(withNoHttpTransferCache(), withIncrementalHydration() ),
    provideHttpClient(withFetch(), withInterceptorsFromDi()),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: CacheInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ForwardHostInterceptor, multi: true },
    SsrCookieService
  ]
})
export class AppModule { }
