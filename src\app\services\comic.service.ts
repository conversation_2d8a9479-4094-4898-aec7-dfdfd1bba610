import { isPlatformServer } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Announcement, Chapter, ChapterPage, Comic, ComicList, IServiceResponse, TopType, UserExpType } from '@schema';
import { catchError, Observable, of } from 'rxjs';
import { ChapterServer } from '../dataSource/schema/ChapterServer';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class ComicService {
  constructor(
    private httpclient: HttpClient,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: object
  ) { }

  getHotComics(page = 1, step = 30) {
    const req = this.httpclient.get<IServiceResponse<ComicList>>(
      `${this.urlService.apiUrl}/hotcomics?${new URLSearchParams({ page: page.toString(), step: step.toString() })}`,
      {
        transferCache: true,
        params: { expiration: 300 }
      }

    );

    return req;
  }
  getComics(
    params: { page: string; step: string; genre: string; sort: string; status: string }
  ) {

    const searchParams = new URLSearchParams(params).toString();
    const req = this.httpclient.get<IServiceResponse<ComicList>>(
      `${this.urlService.apiUrl}/comics?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 }

      }
    );
    return req;
  }
  getRecommendComics() {
    return this.httpclient.get<IServiceResponse<Comic[]>>(`${this.urlService.apiUrl}/comic/recommend`,
      {
        transferCache: true,
        params: { expiration: 600 }

      }
    ).pipe(
      catchError((err) => {
        let errRes: IServiceResponse<Comic[]> = { data: [], status: 0, message: err };
        return of(errRes);
      })
    );

  }
  getComicById(id: string | number): Observable<IServiceResponse<Comic>> {
    const chaptercount = isPlatformServer(this.platformId) ? 24 : -1;
    return this.httpclient.get<IServiceResponse<Comic>>(
      `${this.urlService.apiUrl}/comic/${id}?chaptercount=${chaptercount}`,
      {
        transferCache: true,
        params: { expiration: 120 }
      }
    )
  }


  getComicsByIds(ids: number[]): Observable<IServiceResponse<Comic>> {
    return this.httpclient.get<IServiceResponse<Comic>>(
      `${this.urlService.apiUrl}/comicsbyids?ids=${ids.join(',')}`,
      {
        transferCache: true,
        params: { expiration: 300 }
      }
    );
  }

  getChapterImgs(comicKey: string, chapterKey: string) {
    return this.httpclient.get<IServiceResponse<ChapterPage>>(`${this.urlService.apiUrl}/comic/${comicKey}/chapter/${chapterKey}`,
      {
        transferCache: true,
        params: { expiration: 300 }
      }
    );
  }
  getChapters(comicid: number) {
    return this.httpclient.get<IServiceResponse<Chapter[]>>(`${this.urlService.apiUrl}/comic/${comicid}/chapters`, {
      transferCache: true,
      params: { expiration: 60 }
    });
  }
  getTopComics(type: TopType) {
    return this.httpclient.get<IServiceResponse<Comic[]>>(`${this.urlService.apiUrl}/comic/topview?type=${type}`
      , {
        transferCache: true,
        params: { expiration: 600 }
      }
    );
  }
  getSearchComic(key: string) {
    return this.httpclient.get<IServiceResponse<Comic[]>>(`${this.urlService.apiUrl}/comic/search?keyword=${key}`);
  }

  /**
   * Get comics by author
   */
  getComicsByAuthor(author: string, size: number = 20) {
    const params = new URLSearchParams({
      author: author,
      size: size.toString()
    });

    return this.httpclient.get<IServiceResponse<Comic[]>>(
      `${this.urlService.apiUrl}/comicsbyauthor?${params}`,
      {
        transferCache: true,
        params: { expiration: 300 }
      }
    ).pipe(
      catchError((err) => {
        let errRes: IServiceResponse<Comic[]> = { data: [], status: 0, message: err };
        return of(errRes);
      })
    );
  }
  getAdvanceSearchComic(
    page = 1,
    step = 40,
    sort = 1,
    status = -1,
    genres = '',
    nogenres = '',
    year = -1,
    keyword = ''
  ) {
    const params = {
      page: page.toString(),
      step: step.toString(),
      sort: sort.toString(),
      status: status.toString(),
      genres: genres,
      nogenres: nogenres,
      year: year.toString(),
      keyword: keyword,
    };

    const searchParams = new URLSearchParams(params).toString();

    const req = this.httpclient.get(
      `${this.urlService.apiUrl}/comic/advance?${searchParams}`
    );
    return req;
  }

  getSimilarComic(idcomic: number) {
    const req = this.httpclient.get(
      `${this.urlService.apiUrl}/comic/similar/${idcomic}`,
      {
        transferCache: true,
        params: { expiration: 300 }
      }
    );
    return req;
  }

  updateViewAndExp(
    comicId: number,
    chapterId: number,
    exp: UserExpType.Chapter
  ) {
    const req = this.httpclient.get(
      `${this.urlService.apiUrl}/comic/view_exp?comicId=${comicId}&exp=${exp}&chapterId=${chapterId}`
    );
    return req;
  }
  getAnouncement(): Observable<IServiceResponse<Announcement[]>> {
    const req = this.httpclient.get<IServiceResponse<Announcement[]>>(
      `${this.urlService.apiUrl}/announcement`
      , {
        transferCache: true,
        params: { expiration: 300 }
      }
    );
    return req;
  }

  getChapterServer(ServerId: number) {
    const req = this.httpclient.get<IServiceResponse<ChapterServer>>(
      `${this.urlService.apiUrl}/comic/chapter-server`,
     {
      params: { serverId: ServerId }
     }
    );
    return req;
  }
}
