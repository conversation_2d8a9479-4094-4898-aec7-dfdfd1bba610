import type { Config } from 'tailwindcss'

const config: Config = {
  prefix: '',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  plugins: [require('@tailwindcss/container-queries')],

  theme: {
    extend: {
      colors: {
        'light-bg': '#fff',
        'dark-bg': '#191A1C',
        // primary: {
        //   50: '#FFB3A0',
        //   100: '#E83A3A',
        //   200: '#D1202A',
        //   300: '#7A0000',
        //   400: '#660000',
        //   500: '#5F0000',
        // },
        primary: {
          50: '#FFC49B',
          100: '#F86E4C',
          200: '#D04C2E',
          300: '#A92811',
          400: '#820000',
          500: '#5F0000',
        },
        secondary: {
          100: '#050505',
        },
      },

    },
  },
}

export default config
